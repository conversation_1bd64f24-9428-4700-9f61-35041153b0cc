﻿// Decompiled with JetBrains decompiler
// Type: MPOST.InvalidCommandEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("*************-4784-B26F-29C90942B40B")]
  [ComVisible(true)]
  public delegate void InvalidCommandEventHandler(object sender, EventArgs e);
}
