﻿using System.Windows;

namespace MobileWallet.Desktop
{
    /// <summary>
    /// Interaction logic for Window3.xaml
    /// </summary>
    public partial class CardAlignWithTheCamera : Window
    {
        public CardAlignWithTheCamera()
        {
            InitializeComponent();
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            SelectYourID NewWindow = new SelectYourID();
            NewWindow.Show();
            this.Close();
        }

        private void Button_Click_Cancel(object sender, RoutedEventArgs e)
        {
            WelcomeToMobileWallet NewWindow = new WelcomeToMobileWallet();
            NewWindow.Show();
            this.Close();
        }

        private void Button_Click_Capture(object sender, RoutedEventArgs e) { }

        private void SelectImage_Click(object sender, EventArgs e) { }

        private void ConvertToText_Click(object sender, EventArgs e) { }
    }
}
