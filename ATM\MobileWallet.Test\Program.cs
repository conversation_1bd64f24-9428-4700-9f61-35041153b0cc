﻿using System;
using System.Runtime.InteropServices;
using MobileWallet.Desktop;
using MobileWallet.Desktop.API;
using MobileWallet.Desktop.Atm;

class Program
{
    static void Main(string[] args)
    {
        var list = new List<DenominationRecordDto>()
        {
            new DenominationRecordDto()
            {
                CassetteNo = "1",
                Denomination = 1,
                Count = 3,
            },
            new DenominationRecordDto()
            {
                CassetteNo = "2",
                Denomination = 2,
                Count = 0,
            },
            new DenominationRecordDto()
            {
                CassetteNo = "3",
                Denomination = 5,
                Count = 2,
            },
            new DenominationRecordDto()
            {
                CassetteNo = "4",
                Denomination = 10,
                Count = 4,
            },
            new DenominationRecordDto()
            {
                CassetteNo = "5",
                Denomination = 20,
                Count = 0,
            },
            new DenominationRecordDto()
            {
                CassetteNo = "6",
                Denomination = 50,
                Count = 0,
            },
            new DenominationRecordDto()
            {
                CassetteNo = "7",
                Denomination = 100,
                Count = 0,
            },
        };
        var result = WithdrawHelper.GetNotesV2(list, 15);
        Console.WriteLine(result.Any(p => p.Count > 0));
    }
}
