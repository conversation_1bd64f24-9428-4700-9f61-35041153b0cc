﻿// Decompiled with JetBrains decompiler
// Type: MPOST.FailureClearedEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("6E8F3591-CBEE-46b2-A5AD-C06DF58A2D55")]
  [ComVisible(true)]
  public delegate void FailureClearedEventHandler(object sender, EventArgs e);
}
