﻿@startuml ATM_App_Customer_Flow

!define RECTANGLE class


' Sequence Diagram for Customer Interaction
@startuml ATM_Sequence_Customer_Interaction

actor Customer
entity ATM
entity API
entity Crypto


' Transaction Type Selection
alt Buy Crypto
  ATM -> ATM : Navigate to Buy Process
  Customer -> ATM : Scan ID/Passport (Old/New)
  ATM -> API : Verify ID/Passport (Old/New)
  ATM -> Customer : Request Mobile No
  Customer -> ATM : Enter Mobile No
  ATM -> API : Verify Mobile No
  API -> ATM : Return Mobile Validation
  ATM -> API : Request Token List
  API -> Crypto : Request Token List
  Crypto -> API : Return Token List
  API -> ATM : Return Token List
  Customer -> ATM: Select Token
  ATM -> API : Request Network List
  API -> Crypto : Request Network List
  Crypto -> API : Return Network List
  API -> ATM : Return Network List
  Customer -> ATM: Select Network
  ATM -> ATM : Wallet Address Validation Screen
  Customer -> ATM: Enter Address of Wallet
  ATM -> Crypto: Request Wallet Validation based on Network
  Crypto -> ATM: Return Wallet Validation Result based on Network
  ATM -> ATM : Crypto Quote Verification Screen
  Customer -> ATM: Enter Amount
  ATM -> Crypto: Request Quote
  Crypto -> ATM: Return Quote Details
  Customer -> ATM: Proceed Forward
  ATM -> ATM : Deposit Cash Screen
  Customer -> ATM : Enter Deposit Amount
  ATM -> Crypto : Process Deposit
  Crypto -> API : Transfer Funds
  API -> Crypto : Confirm Transfer
  ATM -> Customer : Show Thank You Page
else Sell
  ATM -> ATM : Navigate to Withdraw Process
  Customer -> ATM : Scan ID/Passport (Old/New)
  ATM -> API : Verify ID/Passport (Old/New)
  ATM -> Customer : Request Mobile No
  Customer -> ATM : Enter Mobile No
  ATM -> API : Verify Mobile No
  API -> ATM : Return Mobile Validation
  ATM -> API : Request Token List
  API -> Crypto : Request Token List
  Crypto -> API : Return Token List
  API -> ATM : Return Token List
  Customer -> ATM: Select Token
  ATM -> API : Request Network List
  API -> Crypto : Request Network List
  Crypto -> API : Return Network List
  API -> ATM : Return Network List
  Customer -> ATM: Select Network
  ATM -> Customer : Show Withdraw Screen
  Customer -> ATM : Enter Withdrawal Amount
  ATM -> Crypto: Request Quote
  Crypto -> ATM: Return Quote Details
  Customer -> ATM: Proceed Forward
  ATM -> Crypto : Process Withdrawal
  Crypto -> API : Withdraw Funds
  API -> Crypto : Confirm Transaction
  ATM -> Customer : Show Thank You Page
else Resume
  ATM -> ATM : Navigate to Resume Process
  Customer -> ATM: Enter Transaction Id
  ATM -> Crypto: Request Transaction Status
  Crypto -> ATM: Return Transaction Status Result
  ATM -> Crypto : Process Withdrawal
  Crypto -> API : Withdraw Funds
  API -> Crypto : Confirm Transaction
  ATM -> Customer : Show Thank You Page
end

@enduml
