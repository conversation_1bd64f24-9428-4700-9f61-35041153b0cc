﻿using System.ComponentModel;
using System.Text;
using System.Windows;
using System.Windows.Documents;
using System.Windows.Media.Imaging;
using MobileWallet.Desktop.API;
using MobileWallet.Desktop.Client;
using MobileWallet.Desktop.Helpers;

namespace MobileWallet.Desktop;

public partial class SelectCryptoQuotation : Window
{
    public SelectCryptoQuotation()
    {
        InitializeComponent();
        Set_Language();
    }

    private void Set_Language()
    {
        switch (Global.DefaultLanguage)
        {
            case "English":
                {
                    MyHeader.Text = ResourceEnglish.VerifyCryptoRates;
                    Next.Content = ResourceEnglish.btnnext;
                    Submit.Content = ResourceEnglish.Submit;
                    Back.Content = ResourceEnglish.Back;
                    Cancel.Content = ResourceEnglish.Cancel;
                    btndone.Content = ResourceEnglish.btndone;
                    btnclear.Content = ResourceEnglish.btnclear;
                    lblMsgOTP.Content = Global.IsDeposit
                        ? ResourceEnglish.PleaseEnterAmountDeposit
                        : ResourceEnglish.PleaseEnterAmountWithdraw;
                }
                break;
            case "French":
                {
                    MyHeader.Text = ResourceFrench.VerifyCryptoRates;
                    Next.Content = ResourceFrench.btnnext;
                    Submit.Content = ResourceFrench.Submit;
                    Back.Content = ResourceFrench.Back;
                    Cancel.Content = ResourceFrench.Cancel;
                    btndone.Content = ResourceFrench.btndone;
                    btnclear.Content = ResourceFrench.btnclear;
                    lblMsgOTP.Content = Global.IsDeposit
                        ? ResourceFrench.PleaseEnterAmountDeposit
                        : ResourceFrench.PleaseEnterAmountWithdraw;
                }
                break;
        }
    }

    private async Task FetchRates()
    {
        try
        {
            App.ShowProcessingDialog();
            var isBuy = Global.IsDeposit;
            var data = await new CryptoClient(
                HttpClientSingleton.Instance
            ).Crypto_GetCryptoQuoteAsync(
                isBuy,
                double.Parse(txtAccountNumber.Text),
                Global.SelectedToken.Address,
                Global.Currency
            );
            if (data != null)
            {
                App.HideProcessingDialog();
                var result = new SelectCryptoRateInfoDialog(
                    data,
                    double.Parse(txtAccountNumber.Text),
                    true
                ).ShowDialog();
                if (result == true)
                {
                    Button_Click_Next(this, null);
                }
            }
        }
        catch (Exception e)
        {
            App.AppLogger.Error(e, e.Message);
            CustomMessageBox.Show("Unable to fetch Rates");
        }
        finally
        {
            App.HideProcessingDialog();
        }
    }

    private void SelectCryptoQuotation_OnLoaded(object sender, RoutedEventArgs e)
    {
        try
        {
            App.ShowProcessingDialog();
            App.StartTimer(this);
            _ = App.TrackAtmRealTime(
                new UpdateAtmRealTimeRequestModel() { CurrentScreen = nameof(SelectCryptoNetwork) }
            );
        }
        catch (Exception exception)
        {
            App.AppLogger.Error(exception, exception.Message);
            App.HideProcessingDialog();
            CustomMessageBox.Show("Unable to fetch quotations for this Token");
        }
        finally
        {
            App.HideProcessingDialog();
        }
    }

    private void Button_Click_Back(object sender, RoutedEventArgs e)
    {
        Global.SelectedToken = null;
        SelectCryptoToken window = new SelectCryptoToken();
        window.Show();
        Close();
    }

    private void Button_Click_Cancel(object sender, RoutedEventArgs e)
    {
        WelcomeToMobileWallet window = new WelcomeToMobileWallet();
        window.Show();
        Close();
    }

    private void SetAccountNumber(string value)
    {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.Append(value);
        string finalvalue = stringBuilder.ToString();
        if (txtAccountNumber.Text.Length >= 9)
        {
            return;
        }

        txtAccountNumber.Text += finalvalue;
    }

    private void btn2_Click(object sender, RoutedEventArgs e)
    {
        SetAccountNumber(btn2.Content.ToString());
    }

    private void btn3_Click(object sender, RoutedEventArgs e)
    {
        SetAccountNumber(btn3.Content.ToString());
    }

    private void btn4_Click(object sender, RoutedEventArgs e)
    {
        SetAccountNumber(btn4.Content.ToString());
    }

    private void btn5_Click(object sender, RoutedEventArgs e)
    {
        SetAccountNumber(btn5.Content.ToString());
    }

    private void btn6_Click(object sender, RoutedEventArgs e)
    {
        SetAccountNumber(btn6.Content.ToString());
    }

    private void btn7_Click(object sender, RoutedEventArgs e)
    {
        SetAccountNumber(btn7.Content.ToString());
    }

    private void btn8_Click(object sender, RoutedEventArgs e)
    {
        SetAccountNumber(btn8.Content.ToString());
    }

    private void btn9_Click(object sender, RoutedEventArgs e)
    {
        SetAccountNumber(btn9.Content.ToString());
    }

    private void btn0_Click(object sender, RoutedEventArgs e)
    {
        SetAccountNumber(btn0.Content.ToString());
    }

    private void btnremovenumber_Click(object sender, RoutedEventArgs e)
    {
        if (!string.IsNullOrWhiteSpace(txtAccountNumber.Text))
        {
            int length = txtAccountNumber.Text.Length;
            string restnumbers = txtAccountNumber.Text.Substring(0, length - 1);
            txtAccountNumber.Text = restnumbers;
        }
    }

    private void btnclear_Click(object sender, RoutedEventArgs e)
    {
        txtAccountNumber.Text = string.Empty;
    }

    private void btn1_Click_1(object sender, RoutedEventArgs e)
    {
        SetAccountNumber(btn1.Content.ToString());
    }

    private async void Button_Click_Submit(object sender, RoutedEventArgs e)
    {
        try
        {
            ButtonHelper.ToggleButton(sender);
            if (string.IsNullOrWhiteSpace(txtAccountNumber.Text))
            {
                CustomMessageBox.Show("Please enter amount to get the crypto rate");
                return;
            }
            var amount = double.Parse(txtAccountNumber.Text);
            if (Global.IsDeposit)
            {
                if (Global.UseV2)
                {
                    if (amount < 5)
                    {
                        CustomMessageBox.Show("Amount cannot be less than $5");
                        return;
                    }
                    if (amount % 1 != 0)
                    {
                        CustomMessageBox.Show("Amount should be divisible by 1");
                        return;
                    }

                    if (amount > 5000)
                    {
                        CustomMessageBox.Show("Amount should be less than $5000");
                        return;
                    }
                }
                else
                {
                    if (amount < 500)
                    {
                        CustomMessageBox.Show("Amount cannot be less than XAF 500");
                        return;
                    }
                    if (amount % 500 != 0)
                    {
                        CustomMessageBox.Show("Amount should be divisible by 500");
                        return;
                    }

                    if (amount > 300000)
                    {
                        CustomMessageBox.Show("Amount should be less than 300000");
                        return;
                    }
                }
            }
            else
            {
                if (Global.UseV2)
                {
                    if (amount < 5)
                    {
                        CustomMessageBox.Show("Amount cannot be less than $5");
                        return;
                    }
                    if (amount % 1 != 0)
                    {
                        CustomMessageBox.Show("Amount should be divisible by 1");
                        return;
                    }

                    if (amount > 5000)
                    {
                        CustomMessageBox.Show("Amount should be less than $5000");
                        return;
                    }
                }
                else
                {
                    if (amount < 5000)
                    {
                        CustomMessageBox.Show("Amount cannot be less than XAF 5000");
                        return;
                    }
                    if (amount % 1000 != 0)
                    {
                        CustomMessageBox.Show("Amount should be divisible by 1000");
                        return;
                    }
                }
            }

            await FetchRates();
        }
        finally
        {
            ButtonHelper.ToggleButton(sender);
        }
    }

    private void Button_Click_Next(object sender, RoutedEventArgs e)
    {
        if (Global.IsDeposit)
        {
            PleaseEnterAddress note = new PleaseEnterAddress();
            note.Show();
            this.Close();
        }
        else
        {
            if (Global.UseV2)
            {
                PleaseEnterAmountV2 note = new PleaseEnterAmountV2();
                note.Show();
            }
            else
            {
                PleaseEnterAmount note = new PleaseEnterAmount();
                note.Show();
            }
            this.Close();
        }
    }

    private void SelectCryptoQuotation_OnClosing(object? sender, CancelEventArgs e)
    {
        App.StopTimer();
    }

    private void Btndone_OnClick(object sender, RoutedEventArgs e)
    {
        if (!string.IsNullOrWhiteSpace(txtAccountNumber.Text))
        {
            int length = txtAccountNumber.Text.Length;
            string restnumbers = txtAccountNumber.Text.Substring(0, length - 1);
            txtAccountNumber.Text = restnumbers;
        }
    }
}
