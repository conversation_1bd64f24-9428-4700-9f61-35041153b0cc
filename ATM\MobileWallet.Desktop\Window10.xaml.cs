﻿using System.Text;
using System.Windows;

namespace MobileWallet.Desktop
{
    /// <summary>
    /// Interaction logic for Window2.xaml
    /// </summary>
    public partial class Window10 : Window
    {
        public Window10()
        {
            InitializeComponent();
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            SelectLanguage NewWindow = new SelectLanguage();
            NewWindow.Show();
            this.Close();
        }

        private void Button_Click_1(object sender, RoutedEventArgs e)
        {
            SelectCashOut NewWindow = new SelectCashOut();
            NewWindow.Show();
            this.Close();
        }

        private void Button_Click_2(object sender, RoutedEventArgs e)
        {
            SelectMobileMoney mainWindow = new SelectMobileMoney();
            mainWindow.Show();
            this.Close();
        }

        private void SetAccountNumber(string value)
        {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.Append(value);
            string finalvalue = stringBuilder.ToString();
            txtAccountNumber.Text += finalvalue;
        }

       

        private void btn2_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn2.Content.ToString());
        }

        private void btn3_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn3.Content.ToString());
        }

        private void btn4_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn4.Content.ToString());
        }

        private void btn5_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn5.Content.ToString());
        }

        private void btn6_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn6.Content.ToString());
        }

        private void btn7_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn7.Content.ToString());
        }

        private void btn8_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn8.Content.ToString());
        }

        private void btn9_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn9.Content.ToString());
        }

        private void btn0_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn0.Content.ToString());
        }

        
        private void btnclear_Click(object sender, RoutedEventArgs e)
        {
            txtAccountNumber.Text = string.Empty;
        }

        private void btn1_Click_1(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn1.Content.ToString());
        }

        private void btndone_Click(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(txtAccountNumber.Text))
            {
                int length = txtAccountNumber.Text.Length;
                string restnumbers = txtAccountNumber.Text.Substring(0, length - 1);
                txtAccountNumber.Text = restnumbers;

            }
        }
    }
}


