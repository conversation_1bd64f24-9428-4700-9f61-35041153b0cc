﻿using System.ComponentModel;
using System.Drawing;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using ITL;
using ITL.Events;
using MobileWallet.Desktop.API;
using MobileWallet.Desktop.Atm;
using MobileWallet.Desktop.Client;
using MobileWallet.Desktop.Extensions;
using MobileWallet.Desktop.Helpers;
using QRCoder;

namespace MobileWallet.Desktop;

public partial class PleaseEnterTransactionIdV2 : CashDeviceWindow
{
    private GetCryptoQuoteByIdResponseModel? data;

    public PleaseEnterTransactionIdV2()
    {
        InitializeComponent();
        SetLanguage();
    }

    public void SetLanguage()
    {
        if (Global.IsFrench)
        {
            WindowTitle.Text = ResourceFrench.PlaceYourQrCodeAlignWithTheCamera;
        }
        else
        {
            WindowTitle.Text = ResourceEnglish.PlaceYourQrCodeAlignWithTheCamera;
        }
    }

    private void Button_Click_Cancel(object sender, RoutedEventArgs e)
    {
        WelcomeToMobileWallet window = new WelcomeToMobileWallet();
        window.Show();
        this.Close();
    }

    private void Button_Click(object sender, RoutedEventArgs e)
    {
        SelectCryptoCashInCashOut window = new SelectCryptoCashInCashOut();
        window.Show();
        Close();
    }

    private async void Button_Click_Submit(object sender, RoutedEventArgs e)
    {
        try
        {
            ButtonHelper.ToggleButton(sender);
            App.ShowProcessingDialog();
            var cryptoClient = new CryptoClient(HttpClientSingleton.Instance);
            this.data = await cryptoClient.Crypto_GetCryptoQuoteByIdAsync(
                TxtTransactionId.Text,
                TokenManager.SessionId
            );
            if (data != null)
            {
                if (data.Data.Status == "Pending")
                {
                    App.HideProcessingDialog();
                    CustomMessageBox.Show(
                        "Transaction is not completed yet. Please try again later"
                    );
                    return;
                }

                if (data.Data.Status == "Failed")
                {
                    App.HideProcessingDialog();
                    CustomMessageBox.Show("Transaction has been failed.");
                    return;
                }

                var amount = data.Data.Amount;
                if (data.Data.IsRedeemed)
                {
                    App.HideProcessingDialog();
                    CustomMessageBox.Show("This Transaction has already been Redeemed");
                    return;
                }

                if (!await WithdrawHelper.IsAvailableCurrencyV2((int)amount))
                {
                    App.HideProcessingDialog();
                    CustomMessageBox.Show("Notes Not Available for XAF " + amount);
                    return;
                }
                _ = App.TrackAtmRealTime(new UpdateAtmRealTimeRequestModel() { IsWithdraw = true });
                var dispenseStatus = App.CashDevice.DispenseValue(
                    new ValueCountryCode((uint)amount * 100, Global.Currency)
                );
                if (dispenseStatus == DispenseTransactionRequestResult.OK) { }
            }
        }
        catch (Exception exception)
        {
            App.AppLogger.Error(exception, exception.Message);
            App.HideProcessingDialog();
            CustomMessageBox.Show("No Transaction to Redeem");
        }
        finally
        {
            ButtonHelper.ToggleButton(sender);
        }
    }

    private async void PleaseEnterTransactionId_OnLoaded(object sender, RoutedEventArgs e)
    {
        Helper.AdjustRowHeight(this, DynamicRow, 300);
        App.StartTimer(this);
        _ = App.TrackAtmRealTime(
            new UpdateAtmRealTimeRequestModel()
            {
                CurrentScreen = nameof(PleaseEnterTransactionIdV2),
            }
        );
        TxtTransactionId.Focus();
        InitCashDevice();
        var cashDevice = App.CashDevice;
        cashDevice.DisableAcceptor();
        cashDevice.EnablePayout();
        cashDevice.EnablePayoutDevice();
    }

    private void PleaseEnterTransactionId_OnClosing(object? sender, CancelEventArgs e)
    {
        UnInitCashDevice();
        App.StopTimer();
    }

    protected override void DispenserTransactionEventHandler(
        object sender,
        DispenserTransactionArgs e
    )
    {
        base.DispenserTransactionEventHandler(sender, e);
        if (e.State == DispenserTransactionState.COMPLETED)
        {
            Dispatcher.Invoke(async () =>
            {
                try
                {
                    if (data == null)
                    {
                        return;
                    }
                    double cashIn = data.Data.Amount;
                    string transactionId = data.Data.TransactionId;
                    string date = DateTime.Now.ToString("yyyy-MM-dd");
                    string time = DateTime.Now.ToString("hh:mm tt");
                    string currency = Global.Currency;
                    string accountNo = data.Data.PhoneNumber;
                    try
                    {
                        var cryptoClient = new CryptoClient(HttpClientSingleton.Instance);
                        await cryptoClient.Crypto_RedeemQuoteAsync(TxtTransactionId.Text);
                    }
                    catch (Exception exception)
                    {
                        App.AppLogger.Error(exception, exception.Message);
                        App.HideProcessingDialog();
                        CustomMessageBox.Show("No Transaction to Redeem");
                        return;
                    }
                    bool isSuccess = ReceiptPrinter.PrintWithdrawReceiptCrypto(
                        TokenManager.UserName,
                        "",
                        cashIn,
                        transactionId,
                        date,
                        time,
                        currency,
                        accountNo,
                        data.Data.AmountIn.ToString("N6"),
                        data.Data.Symbol,
                        data.Data.GasFee.ToString("N4"),
                        data.Data.AmountIn.ToString("N6"),
                        data.Data.Address,
                        Global.UserAddress,
                        data.Data.TransactionHash,
                        null,
                        $"1 {data.Data.Symbol} = $" + data.Data.AvgPrice,
                        "1 USD = " + "NO RATE" + " " + Global.Currency,
                        "$" + data.Data.TotalUsd,
                        "CONFIRMED",
                        data.Data.ExternalTransactionId
                    );
                    if (true)
                    {
                        App.HideProcessingDialog();
                        CustomMessageBox.ShowDialog(
                            Global.IsFrench
                                ? "Veuillez recuperer vos billets !!"
                                : "Please collect your money!!"
                        );
                        ThankYou thankYou = new ThankYou();
                        thankYou.Show();
                        Close();
                    }
                }
                catch (Exception exception)
                {
                    App.AppLogger.Error(exception, exception.Message);
                    Console.WriteLine(exception);
                    App.HideProcessingDialog();
                    CustomMessageBox.Show(
                        "Issue processing transaction please put the money back :)"
                    );
                    return;
                }
            });
        }
        else if (e.State == DispenserTransactionState.ERROR)
        {
            Dispatcher.Invoke(() =>
            {
                App.HideProcessingDialog();
                switch (e.Data.ErrorReason)
                {
                    case DispenserTransactionErrorReason.BUSY:
                        CustomMessageBox.Show(
                            "Transaction was not completed due to the system being busy"
                        );
                        break;
                    case DispenserTransactionErrorReason.NOT_ENOUGH_VALUE:
                    case DispenserTransactionErrorReason.CANNOT_PAY_EXACT_AMOUNT:
                        CustomMessageBox.Show(
                            "Transaction was not completed due to not having enough money"
                        );
                        break;
                    case DispenserTransactionErrorReason.DEVICE_DISABLED:
                        CustomMessageBox.Show(
                            "Transaction was not completed due to device being disabled"
                        );
                        break;
                    case DispenserTransactionErrorReason.ERROR_DURING_PAYOUT:
                        CustomMessageBox.Show("Transaction was not completed due to payout error");
                        break;
                    case DispenserTransactionErrorReason.JAMMED:
                        CustomMessageBox.Show("Transaction was not completed due to Jam");
                        break;
                    case DispenserTransactionErrorReason.HALTED:
                        CustomMessageBox.Show("Transaction was not completed due to Halted");
                        break;
                    case DispenserTransactionErrorReason.UNKNOWN:
                        break;
                    case DispenserTransactionErrorReason.TIME_OUT:
                        CustomMessageBox.Show("Transaction was not completed due to timeout error");
                        break;
                }
            });
        }
    }

    private DispatcherTimer? debounceTimer;

    private void TxtTransactionId_OnTextChanged(object sender, TextChangedEventArgs e)
    {
        if (debounceTimer == null)
        {
            debounceTimer = new DispatcherTimer();
            debounceTimer.Interval = TimeSpan.FromMilliseconds(500); // Debounce time
            debounceTimer.Tick += DebounceTimer_Tick;
        }
        debounceTimer.Stop(); // reset timer
        debounceTimer.Start();
    }

    private void DebounceTimer_Tick(object? sender, EventArgs e)
    {
        debounceTimer?.Stop();
        var text = TxtTransactionId.Text.Split(':').Last();
        GenerateQrCode(text);
        if (!string.IsNullOrWhiteSpace(text))
        {
            QrLabel.Text = text;
        }
        TxtTransactionId.Text = "";
    }

    private void GenerateQrCode(string text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return;

        using (QRCodeGenerator qrGenerator = new QRCodeGenerator())
        {
            QRCodeData qrCodeData = qrGenerator.CreateQrCode(text, QRCodeGenerator.ECCLevel.Q);
            using (QRCode qrCode = new QRCode(qrCodeData))
            {
                Bitmap qrBitmap = qrCode.GetGraphic(20);
                CameraImage.Source = qrBitmap.ToBitmapSource();
            }
        }
    }
}
