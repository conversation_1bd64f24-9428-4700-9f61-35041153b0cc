﻿<Window x:Class="MobileWallet.Desktop.CustomMessageBox"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:MobileWallet.Desktop"
        mc:Ignorable="d"
        WindowStartupLocation="CenterScreen"
        WindowStyle="None"
        WindowState="Maximized"
        Title="CustomMessageBox" Height="450" Width="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="110"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Image Grid.Column="0" Grid.Row="0" Grid.RowSpan="3" Stretch="Fill" Source="images/back-drop.png"/>
        <!-- Title -->
        <TextBlock Name="TxtTitle" Text="Welcome To Mobile Wallet" Margin="0,0,0,0" Foreground="White" FontSize="50" HorizontalAlignment="Center" FontWeight="Bold" Grid.Row="0" />

        <!-- Content -->
        <TextBlock 
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            TextWrapping="Wrap" TextAlignment="Center"  Name="TxtContent" Text="Welcome" Margin="0,0,0,0" Foreground="White" FontSize="35" FontWeight="Bold" Grid.Row="1" />

        <!-- Buttons -->
        <StackPanel Orientation="Horizontal" 
                    HorizontalAlignment="Center" 
                    Name="BtnYes"
                    VerticalAlignment="Top"
                    Grid.Row="2"  Margin="10">
            <Border
                VerticalAlignment="Top"
                HorizontalAlignment="Center"
                Margin="10" BorderBrush="#5387FC" BorderThickness="4" Width="100" Background="#ffffff" Height="100" CornerRadius="10"
            >
                <Button Name="BtnOkContent" Margin="0 0 0 0" BorderThickness="0" Background="Transparent" Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                        Canvas.ZIndex="2" Content="OK" Click="Yes_OnClick" />
            </Border> 
        </StackPanel>
        <StackPanel Orientation="Horizontal" 
               HorizontalAlignment="Center" 
               Name="BtnNo"
               VerticalAlignment="Top"
               Grid.Row="2"  Margin="10">
            <Border
           VerticalAlignment="Top"
           HorizontalAlignment="Center"
           Margin="10" BorderBrush="#5387FC" BorderThickness="4" Width="100" Background="#ffffff" Height="100" CornerRadius="10"
       >
                <Button Name="BtnYesContent" Margin="0 0 0 0" BorderThickness="0" Background="Transparent" Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                   Canvas.ZIndex="2" Content="Yes" Click="Yes_OnClick" />
            </Border>
            <Border
           VerticalAlignment="Top"
           HorizontalAlignment="Center"
           Margin="10" BorderBrush="#5387FC" BorderThickness="4" Width="100" Background="#ffffff" Height="100" CornerRadius="10"
       >
                <Button Name="BtnNoContent" Margin="0 0 0 0" BorderThickness="0" Background="Transparent" Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                   Canvas.ZIndex="2" Content="No" Click="No_OnClick" />
            </Border>
        </StackPanel>
    </Grid>
</Window>
