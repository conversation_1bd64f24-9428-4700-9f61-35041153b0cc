﻿// Decompiled with JetBrains decompiler
// Type: MPOST.StackedEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Obsolete("StackedEventHandler is deprecated, please use StackedWithDocInfoEventHandler instead.")]
  [Guid("94A28D46-C44B-4cf6-9A8C-5BF587A18325")]
  [ComVisible(true)]
  public delegate void StackedEventHandler(object sender, EventArgs e);
}
