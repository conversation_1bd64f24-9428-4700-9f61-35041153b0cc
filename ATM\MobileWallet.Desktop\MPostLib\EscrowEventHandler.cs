﻿// Decompiled with JetBrains decompiler
// Type: MPOST.EscrowEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("D615C565-535E-4db8-B7D2-794844D550D7")]
  [ComVisible(true)]
  public delegate void EscrowEventHandler(object sender, EventArgs e);
}
