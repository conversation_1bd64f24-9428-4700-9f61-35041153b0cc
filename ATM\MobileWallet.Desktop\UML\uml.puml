﻿@startuml ATM_App_Customer_Flow

!define RECTANGLE class

' Activity Diagram for Customer Interaction
start
:Login Screen;
if (Login Successful?) then (yes)
  :OTP Page;
  if (OTP Verified?) then (yes)
    :Getting Started Screen;
    :Language Screen;
    :Select Language (English/French);
    :Operator Page;
    :Select Transaction Type;
    if (Deposit?) then (yes)
      :Deposit Process;
      :Identification Page;
      :Scan ID (Old/New);
      :Enter Account No;
      :API Call - Verify Account;
      :Deposit Cash Screen;
      :Confirm Deposit;
      :Thank You Page;
      :Redirect to Getting Started Screen;
    else (Withdraw)
      :Withdraw Process;
      :Account No Page;
      :OTP Page;
      :Withdraw Screen;
      :Confirm Withdraw;
      :Thank You Page;
      :Redirect to Getting Started Screen;
    endif
  else (no)
    :Error Screen (Invalid OTP);
  endif
else (no)
  :Error Screen (Invalid Credentials);
endif
stop

' Sequence Diagram for Customer Interaction
@startuml ATM_Sequence_Customer_Interaction

actor Customer
entity ATM
entity API
entity MTN


' Transaction Type Selection
alt Deposit
  ATM -> ATM : Navigate to Deposit Process
  Customer -> ATM : Scan ID (Old/New)
  ATM -> API : Verify ID (Old/New)
  ATM -> Customer : Request Account No
  Customer -> ATM : Enter Account No
  ATM -> API : Verify Account No
  API -> MTN : Verify Account No
  MTN -> API : Return Account Validation
  API -> ATM : Return Account Validation
  ATM -> ATM : Deposit Cash Screen
  Customer -> ATM : Enter Deposit Amount
  ATM -> MTN : Process Deposit
  MTN -> API : Transfer Funds
  API -> MTN : Confirm Transfer
  ATM -> Customer : Show Thank You Page
else Withdraw
  ATM -> ATM : Navigate to Withdraw Process
  Customer -> ATM : Enter Account No
  ATM -> API : Verify Account No
  API -> MTN : Verify Account No
  MTN -> API : Return Account Validation
  API -> ATM : Return Account Validation
  ATM -> Customer : Request OTP
  Customer -> ATM : Enter OTP
  ATM -> API : Verify OTP
  API -> ATM : Return Success/Failure
  ATM -> Customer : Show Withdraw Screen
  Customer -> ATM : Enter Withdrawal Amount
  ATM -> MTN : Process Withdrawal
  MTN -> API : Withdraw Funds
  API -> MTN : Confirm Transaction
  ATM -> Customer : Show Thank You Page
end

@enduml
