﻿#pragma checksum "..\..\..\SelectMobileMoney.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "339B09CBCE66448D5118EE12D8D9B9CEDA341538"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MobileWallet.Desktop {
    
    
    /// <summary>
    /// SelectMobileMoney
    /// </summary>
    public partial class SelectMobileMoney : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 15 "..\..\..\SelectMobileMoney.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RowDefinition DynamicRow;
        
        #line default
        #line hidden
        
        
        #line 22 "..\..\..\SelectMobileMoney.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectMoneyOperator;
        
        #line default
        #line hidden
        
        
        #line 24 "..\..\..\SelectMobileMoney.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border BtnMtn;
        
        #line default
        #line hidden
        
        
        #line 25 "..\..\..\SelectMobileMoney.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MtnMoney;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\SelectMobileMoney.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border BtnOrange;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\SelectMobileMoney.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OrangeMoney;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\SelectMobileMoney.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OrangeMoneyContent;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\SelectMobileMoney.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border BtnCrypto;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\SelectMobileMoney.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CryptoMoney;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\SelectMobileMoney.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CryptoMoneyContent;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\SelectMobileMoney.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Back;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\SelectMobileMoney.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Cancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MobileWalletDesk;component/selectmobilemoney.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\SelectMobileMoney.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 8 "..\..\..\SelectMobileMoney.xaml"
            ((MobileWallet.Desktop.SelectMobileMoney)(target)).Loaded += new System.Windows.RoutedEventHandler(this.SelectMobileMoney_OnLoaded);
            
            #line default
            #line hidden
            
            #line 9 "..\..\..\SelectMobileMoney.xaml"
            ((MobileWallet.Desktop.SelectMobileMoney)(target)).Closing += new System.ComponentModel.CancelEventHandler(this.SelectMobileMoney_OnClosing);
            
            #line default
            #line hidden
            return;
            case 2:
            this.DynamicRow = ((System.Windows.Controls.RowDefinition)(target));
            return;
            case 3:
            this.SelectMoneyOperator = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.BtnMtn = ((System.Windows.Controls.Border)(target));
            return;
            case 5:
            this.MtnMoney = ((System.Windows.Controls.Button)(target));
            
            #line 26 "..\..\..\SelectMobileMoney.xaml"
            this.MtnMoney.Click += new System.Windows.RoutedEventHandler(this.Button_Click_MTN);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BtnOrange = ((System.Windows.Controls.Border)(target));
            return;
            case 7:
            this.OrangeMoney = ((System.Windows.Controls.Button)(target));
            
            #line 40 "..\..\..\SelectMobileMoney.xaml"
            this.OrangeMoney.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Orange);
            
            #line default
            #line hidden
            return;
            case 8:
            this.OrangeMoneyContent = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.BtnCrypto = ((System.Windows.Controls.Border)(target));
            return;
            case 10:
            this.CryptoMoney = ((System.Windows.Controls.Button)(target));
            
            #line 55 "..\..\..\SelectMobileMoney.xaml"
            this.CryptoMoney.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Crypto);
            
            #line default
            #line hidden
            return;
            case 11:
            this.CryptoMoneyContent = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.Back = ((System.Windows.Controls.Button)(target));
            
            #line 66 "..\..\..\SelectMobileMoney.xaml"
            this.Back.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Back);
            
            #line default
            #line hidden
            return;
            case 13:
            this.Cancel = ((System.Windows.Controls.Button)(target));
            
            #line 70 "..\..\..\SelectMobileMoney.xaml"
            this.Cancel.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Cancel);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

