﻿// Decompiled with JetBrains decompiler
// Type: MPOST.ErrorOnSendMessageEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("4E78CC12-B229-497b-B84A-67C399A41EA6")]
  [ComVisible(true)]
  public delegate void ErrorOnSendMessageEventHandler(object sender, AcceptorMessageEventArgs e);
}
