﻿<Window x:Class="MobileWallet.Desktop.Window10"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        WindowStyle="None"
        Title="Window10" Height="1353" Width="1600" WindowState="Maximized">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="150"/>
            <RowDefinition Height="150"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <Image Grid.Column="0" Grid.Row="0" Grid.RowSpan="3" Stretch="Fill" Source="./images/back-drop.png "/>
        <Image Grid.Row="0" Width="250" Source="./images/logo.png" />

        <TextBlock HorizontalAlignment="Center" Grid.Row="1" Margin="0 20 0 0" FontWeight="Bold" Foreground="#ffffff" FontSize="50" TextWrapping="Wrap" Text="Please Enter Amount" />


        <Border Margin="0 180 20 0" HorizontalAlignment="Right" Grid.Row="0" Grid.RowSpan="3" BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
            <Button Margin="0 0 0 0" BorderThickness="0" Background="Transparent" Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                    Click="Button_Click_1"  >Submit</Button>
        </Border>

        <Border Margin="20 180 0 0" HorizontalAlignment="Left" Grid.Row="0" Grid.RowSpan="3" BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
            <Button Margin="0 0 0 0" BorderThickness="0" Background="Transparent" Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                    Click="Button_Click"  >Back</Button>
        </Border>
        <Border Margin="20 450 0 0" HorizontalAlignment="Left"  Grid.Row="0" Grid.RowSpan="3" BorderBrush="#FC5353" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
            <Button Margin="0 0 0 0" BorderThickness="0" Background="Transparent" FontWeight="DemiBold" Foreground="#FC5353" FontSize="35"
                    Click="Button_Click_2"  >Cancel</Button>
        </Border>

        <StackPanel Background="DarkBlue"  Margin="543,115,600,100" Grid.RowSpan="2" Grid.Row="1" Orientation="Vertical">
            <TextBox HorizontalAlignment="Center" Height="60" IsReadOnly="True" Width="500" Margin="0,20"   FontSize="40" FontWeight="Bold" Name="txtAccountNumber" />
            <Grid HorizontalAlignment="Center" Name="numbperpad" >
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition />
                    <ColumnDefinition />
                    <ColumnDefinition />
                </Grid.ColumnDefinitions>

                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>

                <Button Content="1" Grid.Column="0" Grid.Row="0" Height="100" Width="100" Background="Blue" Foreground="White" FontSize="55" Name="btn1" Click="btn1_Click_1" />
                <Button Content="2" Grid.Column="1" Grid.Row="0" Height="100" Width="100" Background="Blue" Foreground="White" FontSize="55" Name="btn2" Click="btn2_Click" />
                <Button Content="3" Grid.Column="2" Grid.Row="0" Height="100" Width="100" Background="Blue" Foreground="White" FontSize="55" Name="btn3" Click="btn3_Click" VerticalAlignment="Top" />

                <Button Content="4" Grid.Column="0" Grid.Row="1" Height="100" Width="100" Background="Blue" Foreground="White" FontSize="55" Name="btn4" Click="btn4_Click" />
                <Button Content="5" Grid.Column="1" Grid.Row="1" Height="100" Width="100" Background="Blue" Foreground="White" FontSize="55" Name="btn5" Click="btn5_Click" />
                <Button Content="6" Grid.Column="2" Grid.Row="1" Height="100" Width="100" Background="Blue" Foreground="White" FontSize="55" Name="btn6" Click="btn6_Click" />

                <Button Content="7" Grid.Column="0" Grid.Row="2" Height="100" Width="100" Background="Blue" Foreground="White" FontSize="55" Name="btn7" Click="btn7_Click" />
                <Button Content="8" Grid.Column="1" Grid.Row="2" Height="100" Width="100" Background="Blue" Foreground="White" FontSize="55" Name="btn8" Click="btn8_Click" />
                <Button Content="9" Grid.Column="2" Grid.Row="2" Height="100" Width="100" Background="Blue" Foreground="White" FontSize="55" Name="btn9" Click="btn9_Click" />

                <Button Content="Clear" Background="LightYellow" Grid.Column="0" Grid.Row="3" Height="100" Width="100" FontSize="20" Name="btnclear" Click="btnclear_Click" />
                <Button Content="0" Background="Blue" Foreground="White" Grid.Column="1" Grid.Row="3" Height="100" Width="100" FontSize="55" Name="btn0" Click="btn0_Click" />
                <Button Content="Backspace" Background="LightYellow" Grid.Column="2" Grid.Row="3" Height="100" Width="100" FontSize="20" Name="btndone" Click="btndone_Click" />


            </Grid>
        </StackPanel>
    </Grid>
</Window>
