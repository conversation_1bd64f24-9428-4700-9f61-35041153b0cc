﻿// Decompiled with JetBrains decompiler
// Type: MPOST.ReturnedEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("3199C7CD-934B-460a-9D5D-A7853348026E")]
  [ComVisible(true)]
  public delegate void ReturnedEventHandler(object sender, EventArgs e);
}
