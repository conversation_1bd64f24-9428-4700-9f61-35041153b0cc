﻿#pragma checksum "..\..\..\SetDenomination.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "6B36A8F8F332ADC3F8927870E439C9F876949037"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MobileWallet.Desktop {
    
    
    /// <summary>
    /// SetDenomination
    /// </summary>
    public partial class SetDenomination : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 63 "..\..\..\SetDenomination.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtDenominationNf1;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\SetDenomination.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtDenominationNf2;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\SetDenomination.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtDenominationNf3;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\SetDenomination.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid numbperpad;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\SetDenomination.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn1;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\SetDenomination.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn2;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\SetDenomination.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn3;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\SetDenomination.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn4;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\SetDenomination.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn5;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\SetDenomination.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn6;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\SetDenomination.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn7;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\SetDenomination.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn8;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\SetDenomination.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn9;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\SetDenomination.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnclear;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\SetDenomination.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn0;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\SetDenomination.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnBackSpace;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\SetDenomination.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnLogin;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MobileWallet.Desktop;V1.0.0.0;component/setdenomination.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\SetDenomination.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 7 "..\..\..\SetDenomination.xaml"
            ((MobileWallet.Desktop.SetDenomination)(target)).Loaded += new System.Windows.RoutedEventHandler(this.SetDenomination_OnLoaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.txtDenominationNf1 = ((System.Windows.Controls.TextBox)(target));
            
            #line 61 "..\..\..\SetDenomination.xaml"
            this.txtDenominationNf1.GotFocus += new System.Windows.RoutedEventHandler(this.GotFocus);
            
            #line default
            #line hidden
            return;
            case 3:
            this.txtDenominationNf2 = ((System.Windows.Controls.TextBox)(target));
            
            #line 65 "..\..\..\SetDenomination.xaml"
            this.txtDenominationNf2.GotFocus += new System.Windows.RoutedEventHandler(this.GotFocus);
            
            #line default
            #line hidden
            return;
            case 4:
            this.txtDenominationNf3 = ((System.Windows.Controls.TextBox)(target));
            
            #line 77 "..\..\..\SetDenomination.xaml"
            this.txtDenominationNf3.GotFocus += new System.Windows.RoutedEventHandler(this.GotFocus);
            
            #line default
            #line hidden
            return;
            case 5:
            this.numbperpad = ((System.Windows.Controls.Grid)(target));
            return;
            case 6:
            this.btn1 = ((System.Windows.Controls.Button)(target));
            
            #line 100 "..\..\..\SetDenomination.xaml"
            this.btn1.Click += new System.Windows.RoutedEventHandler(this.btn1_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.btn2 = ((System.Windows.Controls.Button)(target));
            
            #line 102 "..\..\..\SetDenomination.xaml"
            this.btn2.Click += new System.Windows.RoutedEventHandler(this.btn2_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.btn3 = ((System.Windows.Controls.Button)(target));
            
            #line 104 "..\..\..\SetDenomination.xaml"
            this.btn3.Click += new System.Windows.RoutedEventHandler(this.btn3_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.btn4 = ((System.Windows.Controls.Button)(target));
            
            #line 107 "..\..\..\SetDenomination.xaml"
            this.btn4.Click += new System.Windows.RoutedEventHandler(this.btn4_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.btn5 = ((System.Windows.Controls.Button)(target));
            
            #line 109 "..\..\..\SetDenomination.xaml"
            this.btn5.Click += new System.Windows.RoutedEventHandler(this.btn5_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.btn6 = ((System.Windows.Controls.Button)(target));
            
            #line 111 "..\..\..\SetDenomination.xaml"
            this.btn6.Click += new System.Windows.RoutedEventHandler(this.btn6_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.btn7 = ((System.Windows.Controls.Button)(target));
            
            #line 114 "..\..\..\SetDenomination.xaml"
            this.btn7.Click += new System.Windows.RoutedEventHandler(this.btn7_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.btn8 = ((System.Windows.Controls.Button)(target));
            
            #line 116 "..\..\..\SetDenomination.xaml"
            this.btn8.Click += new System.Windows.RoutedEventHandler(this.btn8_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.btn9 = ((System.Windows.Controls.Button)(target));
            
            #line 118 "..\..\..\SetDenomination.xaml"
            this.btn9.Click += new System.Windows.RoutedEventHandler(this.btn9_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.btnclear = ((System.Windows.Controls.Button)(target));
            
            #line 121 "..\..\..\SetDenomination.xaml"
            this.btnclear.Click += new System.Windows.RoutedEventHandler(this.btnclear_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.btn0 = ((System.Windows.Controls.Button)(target));
            
            #line 130 "..\..\..\SetDenomination.xaml"
            this.btn0.Click += new System.Windows.RoutedEventHandler(this.btn0_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.btnBackSpace = ((System.Windows.Controls.Button)(target));
            
            #line 132 "..\..\..\SetDenomination.xaml"
            this.btnBackSpace.Click += new System.Windows.RoutedEventHandler(this.btnBackSpace_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.BtnLogin = ((System.Windows.Controls.Button)(target));
            
            #line 151 "..\..\..\SetDenomination.xaml"
            this.BtnLogin.Click += new System.Windows.RoutedEventHandler(this.SaveDenomination_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

