﻿using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using MobileWallet.Desktop.API;

namespace MobileWallet.Desktop
{
    /// <summary>
    /// Interaction logic for Window2.xaml
    /// </summary>
    public partial class SelectCashOut : Window
    {
        public SelectCashOut(bool isWithdraw = true, bool isDeposit = true)
        {
            InitializeComponent();
            Set_Language();
            if (!isWithdraw)
            {
                BtnWithdrawBorder.Visibility = Visibility.Hidden;
            }
            if (!isDeposit)
            {
                BtnDepositBorder.Visibility = Visibility.Hidden;
            }
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            SelectLanguage NewWindow = new SelectLanguage();
            NewWindow.Show();
            this.Close();
        }

        //private void Button_Click_1(object sender, RoutedEventArgs e)
        //{

        //    //Deposit.IsEnabled = true;
        //    Application.Current.Properties["Deposit"] = "Deposit";
        //    Application.Current.Properties["Withdraw"] = "null";
        //    EnterAccountNumber enterAccountNumber = new EnterAccountNumber();
        //    enterAccountNumber.Show();
        //    this.Close();
        //}

        private void Button_Click_2(object sender, RoutedEventArgs e)
        {
            SelectMobileMoney mainWindow = new SelectMobileMoney();
            mainWindow.Show();
            this.Close();
        }

        private void Button_Click_Back(object sender, RoutedEventArgs e)
        {
            SelectMobileMoney mainWindow = new SelectMobileMoney();
            mainWindow.Show();
            this.Close();
            // PlaceYourCardAlignWithTheCamera placeYourCard = new PlaceYourCardAlignWithTheCamera();
            // placeYourCard.Show();
            // this.Close();
        }

        private void Button_Click_Cancel(object sender, RoutedEventArgs e)
        {
            _ = App.TrackAtmRealTime(new UpdateAtmRealTimeRequestModel() { Action = "Cancel" });
            WelcomeToMobileWallet NewWindow = new WelcomeToMobileWallet();
            NewWindow.Show();
            this.Close();
        }

        //private void Button_Click_3(object sender, RoutedEventArgs e)
        //{

        //}
        private void Set_Language()
        {
            switch (Global.DefaultLanguage)
            {
                case "English":

                    Back.Content = ResourceEnglish.Back;
                    Cancel.Content = ResourceEnglish.Cancel;
                    Deposit.Content = ResourceEnglish.Deposit;
                    Withdraw.Content = ResourceEnglish.Withdrawal;
                    SelectCashInOut.Text = ResourceEnglish.SelectCashInOut;
                    break;
                case "French":
                    Back.Content = ResourceFrench.Back;
                    Cancel.Content = ResourceFrench.Cancel;
                    Deposit.Content = ResourceFrench.Deposit;
                    Withdraw.Content = ResourceFrench.Withdrawal;
                    SelectCashInOut.Text = ResourceFrench.SelectCashInOut;
                    break;
            }
        }

        private void Button_Click_Deposit(object sender, RoutedEventArgs e)
        {
            Button btn = (Button)sender;
            String deposit = btn.Name.ToString();
            Application.Current.Properties["Deposit"] = deposit;
            Application.Current.Properties["Withdraw"] = "null";
            _ = App.TrackAtmRealTime(new UpdateAtmRealTimeRequestModel() { Action = "Withdraw" });
            _ = App.UploadCameraPicture();
            SelectYourID NewWindow = new SelectYourID();
            NewWindow.Show();
            // EnterAccountNumber enterAccountNumber = new EnterAccountNumber();
            // enterAccountNumber.Show();
            this.Close();
        }

        private void Button_Click_Withdrawl(object sender, RoutedEventArgs e)
        {
            Button btn = (Button)sender;
            String withdraw = btn.Name.ToString();
            Application.Current.Properties["Withdraw"] = withdraw;
            Application.Current.Properties["Deposit"] = "null";
            _ = App.TrackAtmRealTime(new UpdateAtmRealTimeRequestModel() { Action = "Withdraw" });
            EnterAccountNumber enterAccountNumber = new EnterAccountNumber();
            enterAccountNumber.Show();
            this.Close();
        }

        private void SelectCashOut_OnLoaded(object sender, RoutedEventArgs e)
        {
            Helper.AdjustRowHeight(this, DynamicHeight);
            App.StartTimer(this);
            if (Global.Profile != null)
            {
                if (!Global.Profile.DepositEnabled)
                {
                    BtnDepositBorder.Visibility = Visibility.Hidden;
                }
                if (!Global.Profile.WithdrawEnabled)
                {
                    BtnWithdrawBorder.Visibility = Visibility.Hidden;
                }
            }
            _ = App.TrackAtmRealTime(
                new UpdateAtmRealTimeRequestModel() { CurrentScreen = nameof(SelectCashOut) }
            );
        }

        private void SelectCashOut_OnClosing(object? sender, CancelEventArgs e)
        {
            App.StopTimer();
        }
    }
}
