﻿<?xml version="1.0" encoding="utf-8"?>
<root>
    <!-- 
      Microsoft ResX Schema 
      
      Version 2.0
      
      The primary goals of this format is to allow a simple XML format 
      that is mostly human readable. The generation and parsing of the 
      various data types are done through the TypeConverter classes 
      associated with the data types.
      
      Example:
      
      ... ado.net/XML headers & schema ...
      <resheader name="resmimetype">text/microsoft-resx</resheader>
      <resheader name="version">2.0</resheader>
      <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
      <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
      <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
      <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
      <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
          <value>[base64 mime encoded serialized .NET Framework object]</value>
      </data>
      <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
          <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
          <comment>This is a comment</comment>
      </data>
                  
      There are any number of "resheader" rows that contain simple 
      name/value pairs.
      
      Each data row contains a name, and value. The row also contains a 
      type or mimetype. Type corresponds to a .NET class that support 
      text/value conversion through the TypeConverter architecture. 
      Classes that don't support this are serialized and stored with the 
      mimetype set.
      
      The mimetype is used for serialized objects, and tells the 
      ResXResourceReader how to depersist the object. This is currently not 
      extensible. For a given mimetype the value must be set accordingly:
      
      Note - application/x-microsoft.net.object.binary.base64 is the format 
      that the ResXResourceWriter will generate, however the reader can 
      read any of the formats listed below.
      
      mimetype: application/x-microsoft.net.object.binary.base64
      value   : The object must be serialized with 
              : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
              : and then encoded with base64 encoding.
      
      mimetype: application/x-microsoft.net.object.soap.base64
      value   : The object must be serialized with 
              : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
              : and then encoded with base64 encoding.
  
      mimetype: application/x-microsoft.net.object.bytearray.base64
      value   : The object must be serialized into a byte array 
              : using a System.ComponentModel.TypeConverter
              : and then encoded with base64 encoding.
      -->
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:import namespace="http://www.w3.org/XML/1998/namespace"/>
        <xsd:element name="root" msdata:IsDataSet="true">
            <xsd:complexType>
                <xsd:choice maxOccurs="unbounded">
                    <xsd:element name="metadata">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0"/>
                            </xsd:sequence>
                            <xsd:attribute name="name" use="required" type="xsd:string"/>
                            <xsd:attribute name="type" type="xsd:string"/>
                            <xsd:attribute name="mimetype" type="xsd:string"/>
                            <xsd:attribute ref="xml:space"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="assembly">
                        <xsd:complexType>
                            <xsd:attribute name="alias" type="xsd:string"/>
                            <xsd:attribute name="name" type="xsd:string"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="data">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1"/>
                                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2"/>
                            </xsd:sequence>
                            <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1"/>
                            <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3"/>
                            <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4"/>
                            <xsd:attribute ref="xml:space"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="resheader">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1"/>
                            </xsd:sequence>
                            <xsd:attribute name="name" type="xsd:string" use="required"/>
                        </xsd:complexType>
                    </xsd:element>
                </xsd:choice>
            </xsd:complexType>
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>2.0</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
    </resheader>
    <data name="Back" xml:space="preserve">
    <value>Retour</value>
  </data>
    <data name="btnclear" xml:space="preserve">
    <value>Effacer Tout</value>
  </data>
    <data name="btndone" xml:space="preserve">
    <value>Effacer</value>
  </data>
    <data name="btnnext" xml:space="preserve">
    <value>Suivante</value>
  </data>
    <data name="Cancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
    <data name="Capture" xml:space="preserve">
    <value>Capturer</value>
  </data>
    <data name="Confirm Account Number" xml:space="preserve">
    <value>Confirmer le numéro de compte</value>
  </data>
    <data name="Deposit" xml:space="preserve">
    <value>Dépôt</value>
  </data>
    <data name="Buy" xml:space="preserve">
    <value>Acheter</value>
  </data>
    <data name="Sell" xml:space="preserve">
    <value>Vendre</value>
  </data>
    <data name="Resume" xml:space="preserve">
    <value>CV</value>
  </data>
    <data name="Done" xml:space="preserve">
    <value>Terminé</value>
  </data>
    <data name="Driving License" xml:space="preserve">
    <value>Permis de conduire</value>
  </data>
    <data name="English" xml:space="preserve">
    <value>Anglais</value>
  </data>
    <data name="EnterAccountNo" xml:space="preserve">
    <value>Entrez le numéro de compte</value>
  </data>
    <data name="EnterAmount" xml:space="preserve">
    <value>Veuillez entrer le montant (entre 5000Fcfa et 200,000Fcfa)</value>
  </data>
    <data name="EnterAmountUsd" xml:space="preserve">
    <value>Please Enter Amount (between $5 and $5,000)</value>
  </data>
    <data name="French" xml:space="preserve">
    <value>français</value>
  </data>
    <data name="IDCard" xml:space="preserve">
    <value>New Carte d'identité</value>
  </data>
    <data name="lblMsg" xml:space="preserve">
    <value>Veuillez entrer le numéro de compte</value>
  </data>
    <data name="lblMsgEnterAmount" xml:space="preserve">
    <value>Veuillez entrer le montant</value>
  </data>
    <data name="lblMsgOTP" xml:space="preserve">
    <value>Veuillez entrer le code OTP reçu :</value>
  </data>
    <data name="MtnMoney" xml:space="preserve">
    <value>Momo</value>
  </data>
    <data name="OrangeMoney" xml:space="preserve">
    <value>Orange</value>
  </data>
    <data name="Crypto" xml:space="preserve">
    <value>Crypto</value>
  </data>
    <data name="Others" xml:space="preserve">
    <value>Les autres</value>
  </data>
    <data name="Passport" xml:space="preserve">
    <value>Old Id Card/Passeport</value>
  </data>
    <data name="Place Your Card Align With The Camera" xml:space="preserve">
    <value>Placez votre carte alignée avec l'appareil photo</value>
  </data>
    <data name="Please Enter Account Number" xml:space="preserve">
    <value>Veuillez entrer le numéro de compte</value>
  </data>
    <data name="ResendOTP" xml:space="preserve">
    <value>Renvoyer le code</value>
  </data>
    <data name="SecureOTPSend" xml:space="preserve">
    <value>Secure OTP a été envoyé à votre registre no.</value>
  </data>
    <data name="SecureOTPSendWithPhone" xml:space="preserve">
    <value>Un code sécurisé a été envoyé sur le compte {Phone}</value>
  </data>
    <data name="SecureOTPSendWithName" xml:space="preserve">
    <value>Un code sécurisé a été envoyé sur le compte {Phone}\nNom : {Name}</value>
  </data>
    <data name="Select Amount" xml:space="preserve">
    <value>Sélectionnez le montant</value>
  </data>
    <data name="Select Cash Out" xml:space="preserve">
    <value>Sélectionnez Encaissement</value>
  </data>
    <data name="Select Your ID &amp; Insert To The Scanner Area" xml:space="preserve">
    <value>Sélectionnez votre identifiant et insérez-le dans la zone du scanner</value>
  </data>
    <data name="Select Your ID and Insert To The Scanner Area" xml:space="preserve">
    <value>Sélectionnez votre identifiant et insérez-le dans la zone du scanner</value>
  </data>
    <data name="SelectCashInOut" xml:space="preserve">
    <value>Sélectionnez Dépôt ou Retrait</value>
  </data>
    <data name="SelectLaunguage" xml:space="preserve">
    <value>Selectionnez la langue</value>
  </data>
    <data name="SelectMoneyOperator" xml:space="preserve">
    <value>Sélectionnez l'opérateur</value>
  </data>
    <data name="Submit" xml:space="preserve">
    <value>Valider</value>
  </data>
    <data name="Thanks" xml:space="preserve">
    <value>Merci . . .</value>
  </data>
    <data name="TransactionProcessing" xml:space="preserve">
    <value>Traitement de la transaction</value>
  </data>
    <data name="Withdrawal" xml:space="preserve">
    <value>Retrait</value>
  </data>
    <data name="HowMuchDoYouWantToBuy" xml:space="preserve">
    <value>Combien voulez-vous acheter/vendre ?</value>
</data>
    <data name="BuyAboveLimit" xml:space="preserve">
    <value>Achat/vente au-dessus de 10 000 XAF</value>
</data>
    <data name="BuyBelowLimit" xml:space="preserve">
    <value>Achat/vente en dessous de 10 000 XAF</value>
</data>
    <data name="WhatDoYouWantToDo" xml:space="preserve">
    <value>Que voulez-vous faire ?</value>
</data>
    <data name="SelectCryptoNetwork" xml:space="preserve">
    <value>Sélectionnez le réseau crypto</value>
</data>
    <data name="SelectCryptoToken" xml:space="preserve">
    <value>Sélectionnez le jeton crypto</value>
</data>
    <data name="VerifyCryptoRates" xml:space="preserve">
    <value>Vérifiez les taux crypto</value>
</data>
    <data name="Amount" xml:space="preserve">
    <value>Montant</value>
</data>
    <data name="Market" xml:space="preserve">
    <value>Marché</value>
    </data>
    <data name="EstAveragePrice" xml:space="preserve">
    <value>Prix moyen</value>
    </data>
    <data name="EstSlippage" xml:space="preserve">
    <value>Glissement</value>
  </data>
    <data name="PaymentMethod" xml:space="preserve">
    <value>Méthode de paiement</value>
  </data>
    <data name="EstFee" xml:space="preserve">
    <value>Frais</value>
  </data>

    <data name="OutOfService">
        <value>Le distributeur automatique est hors service</value>
    </data>
    <data name="SelectIdentificationMethod" xml:space="preserve">
        <value>Sélectionner la méthode d'identification</value>
    </data>
    <data name="RetakeImage" xml:space="preserve">
     <value>Reprendre l'image</value>
    </data>
    <data name="PlacePassportInsideCamera">
        <value>Placez votre passeport à l'intérieur du scanner</value>
    </data>
    <data name="PlaceIdCardInsideCamera">
        <value>Placez votre carte d'identité devant la caméra</value>
    </data>
    <data name="PlaceYourQrCodeAlignWithTheCamera">
        <value>Please scan your transaction Id below using the QR Code Scanner</value>
    </data>
    <data name="SellAboveLimit" xml:space="preserve">
    <value>Sell above 10,000 XAF</value>
  </data>
    <data name="SellBelowLimit" xml:space="preserve">
    <value>Sell below 10,000 XAF</value>
  </data>
    <data name="PleaseEnterAmountDeposit" xml:space="preserve">
    <value>Please enter amount you want to deposit</value>
  </data>
    <data name="PleaseEnterAmountWithdraw" xml:space="preserve">
    <value>Please enter amount you want to withdraw</value>
  </data>
    <data name="TotalLocal" xml:space="preserve">
    <value>Total Local</value>
  </data>
    <data name="TotalUsd" xml:space="preserve">
    <value>Total USD</value>
  </data>
    <data name="PlaceYourQrCodeAddress">
        <value>Please scan your wallet address below using the QR Code Scanner</value>
    </data>
    <data name="QuoteDeposit">
        <value>you will receive</value>
    </data>
    <data name="QuoteWithdraw">
        <value>you will sell</value>
    </data>
    <data name="ExchangeRate">
        <value>Exchange Rate</value>
    </data>

</root>