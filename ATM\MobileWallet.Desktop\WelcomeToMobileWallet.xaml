﻿<Window x:Class="MobileWallet.Desktop.WelcomeToMobileWallet"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        WindowStyle="None"
        Loaded="OnWindowLoad"
        Title="WelcomeToMobileWallet" Height="1600" Width="900" WindowState="Maximized">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="250" />
            <RowDefinition Height="150" />
            <RowDefinition Height="*" />
            <!-- New row for video -->
        </Grid.RowDefinitions>
        <Image Grid.Column="0" Grid.Row="0" Grid.RowSpan="3" Stretch="Fill" Source="images/back-drop.png"/>
        <Image Grid.Row="0" Source="images/logo.png" Width="200" Margin="0,20,0,0" VerticalAlignment="Top"  HorizontalAlignment="Center"/>
        <TextBlock Margin="0,120,0,0" Foreground="White" FontSize="50" VerticalAlignment="Center" TextAlignment="Center" HorizontalAlignment="Center" FontWeight="Bold" Grid.Row="0">
            Welcome To Mobile Wallet
            <LineBreak/>
            Bienvenue sur le Mobile Wallet
        </TextBlock>
        <MediaElement
            MediaEnded="MediaElement_MediaEnded"
            Source="SampleVedio.mp4" Name="VideoControl" LoadedBehavior="Manual" Grid.Row="2" UnloadedBehavior="Stop" HorizontalAlignment="Center" Margin="0,50,0,0" Height="Auto" Width="Auto" VerticalAlignment="Top" />
        <!--<Button Height="23" HorizontalAlignment="Left" Margin="10,0,0,17" Grid.Row="2"
            Name="PlayButton" VerticalAlignment="Bottom" Width="75"
            Click="PlayClick">Play</Button>
        <Button Height="23" HorizontalAlignment="Left" Margin="100,0,0,17" Grid.Row="2"
            Name="PauseButton" VerticalAlignment="Bottom" Width="75"
            Click="PauseClick">Pause</Button>-->
        <!-- <Border -->
        <!--     Margin="10,0,0,17" HorizontalAlignment="Left" Grid.Row="2" BorderBrush="#5387FC" BorderThickness="4" Width="292" Background="#ffffff" Height="100" CornerRadius="10" -->
        <!--     > -->
        <!--     <Button Margin="0 0 0 0" BorderThickness="0" Background="Transparent" Foreground="#5387FC" FontWeight="DemiBold" FontSize="35" -->
        <!--             Canvas.ZIndex="2" Content="Denomiaton" Click="Set_Denomination" /> -->
        <!-- </Border>  -->

        <Border 
            Margin="0,0,10,0" HorizontalAlignment="Center" Grid.Row="2" BorderBrush="#5387FC" BorderThickness="4" Width="292" Background="#ffffff" Height="100" CornerRadius="10"
            >
            <Button Margin="0 0 0 0" BorderThickness="0" Background="Transparent" Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                    Canvas.ZIndex="2" Click="Button_Click">
                <TextBlock TextAlignment="Center">
                    Getting Started
                    <LineBreak/>
                    Commencer
                </TextBlock>
            </Button>
        </Border>
    </Grid>
</Window>