﻿using System.Net.Http;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace MobileWallet.Desktop
{
    public partial class SetDenomination : Window
    {
        private TextBox selectedTextBox;
        public SetDenomination()
        {
            InitializeComponent();
        }
        private void SetAccountNumber(string value)
        {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.Append(value);
            string finalvalue = stringBuilder.ToString();
            if (selectedTextBox != null) selectedTextBox.Text += finalvalue;
        }

        private void btn1_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn1.Content.ToString());
        }

        private void btn2_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn2.Content.ToString());
        }

        private void btn3_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn3.Content.ToString());
        }

        private void btn4_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn4.Content.ToString());
        }

        private void btn5_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn5.Content.ToString());
        }

        private void btn6_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn6.Content.ToString());
        }

        private void btn7_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn7.Content.ToString());
        }

        private void btn8_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn8.Content.ToString());
        }

        private void btn9_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn9.Content.ToString());
        }

        private void btn0_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn0.Content.ToString());
        }

        private void btnclear_Click(object sender, RoutedEventArgs e)
        {
            if (selectedTextBox != null) selectedTextBox.Text = string.Empty;
        }

        private void btnBackSpace_Click(object sender, RoutedEventArgs e)
        {
            if (selectedTextBox != null)
            {
                if (selectedTextBox.Text.Length > 0)
                {
                    selectedTextBox.Text = selectedTextBox.Text[..^2];
                }
            }   
        }

        private void GotFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox txt)
            {
                selectedTextBox = txt;
            }
        }
        private static IEnumerable<T> FindVisualChildren<T>(DependencyObject depObj) where T : DependencyObject
        {
            if (depObj == null) yield return (T)Enumerable.Empty<T>();
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
            {
                DependencyObject ithChild = VisualTreeHelper.GetChild(depObj, i);
                if (ithChild == null) continue;
                if (ithChild is T t) yield return t;
                foreach (T childOfChild in FindVisualChildren<T>(ithChild)) yield return childOfChild;
            }
        }
        
        private void SaveDenomination_Click(object sender, RoutedEventArgs e)
        {
            var txtBoxes = FindVisualChildren<TextBox>(this).ToList();
            if (txtBoxes.Any())
            {
                var isAllValid = txtBoxes.All(p => int.TryParse(p.Text, out var f) && int.Parse(p.Text) >= 0);
                if (!isAllValid)
                {
                    CustomMessageBox.Show("Please fill all fields with valid details");
                    return;
                }
            }
            //Todo Fix this
            // var client = new TransactionClient(new HttpClient());
            // var records = await client.Transaction_GetDenominationRecordsAsync();
            // if (records.Any())
            // {
            //     var record = records.FirstOrDefault(p => p.CassetteNo == "NF1");
            //     if (record != null)
            //     {
            //         record.Count = int.Parse(txtStackNf1.Text);
            //         record.Denomination = int.Parse(txtDenominationNf1.Text);
            //     }
            //     record = records.FirstOrDefault(p => p.CassetteNo == "NF2");
            //     if (record != null)
            //     {
            //         record.Count = int.Parse(txtStackNf2.Text);
            //         record.Denomination = int.Parse(txtDenominationNf2.Text);
            //     }
            //     record = records.FirstOrDefault(p => p.CassetteNo == "NF3");
            //     if (record != null)
            //     {
            //         record.Count = int.Parse(txtStackNf3.Text);
            //         record.Denomination = int.Parse(txtDenominationNf3.Text);
            //     }
            //     foreach (var denominationRecord in records)
            //     {
            //         await client.Transaction_SaveDenominationRecordAsync(denominationRecord);
            //     }
            //     MessageBox.Show("Saved Successfully");
            // }
            // else
            // {
            //     records.Add(new DenominationRecord()
            //     {
            //         CassetteNo = "NF1",
            //         Denomination = int.Parse(txtDenominationNf1.Text),
            //         Count = int.Parse(txtStackNf1.Text)
            //     });
            //     records.Add(new DenominationRecord()
            //     {
            //         CassetteNo = "NF2",
            //         Denomination = int.Parse(txtDenominationNf2.Text),
            //         Count = int.Parse(txtStackNf2.Text)
            //     });
            //     records.Add(new DenominationRecord()
            //     {
            //         CassetteNo = "NF3",
            //         Denomination = int.Parse(txtDenominationNf3.Text),
            //         Count = int.Parse(txtStackNf3.Text)
            //     });
            //     foreach (var denominationRecord in records)
            //     {
            //         await client.Transaction_SaveDenominationRecordAsync(denominationRecord);
            //     }
            //     CustomMessageBox.ShowDialog("Saved Successfully");
            // }
        }

        private void SetDenomination_OnLoaded(object sender, RoutedEventArgs e)
        {
            //Todo Fix this
            // var client = new TransactionClient(new HttpClient());
            // var records = await client.Transaction_GetDenominationRecordsAsync();
            // if (records.Any())
            // {
            //     var record = records.FirstOrDefault(p => p.CassetteNo == "NF1");
            //     if (record != null)
            //     {
            //         (txtStackNf1.Text) = record.Count.ToString();
            //         (txtDenominationNf1.Text) = record.Denomination.ToString();
            //     }
            //     record = records.FirstOrDefault(p => p.CassetteNo == "NF2");
            //     if (record != null)
            //     {
            //         (txtStackNf2.Text) = record.Count.ToString();
            //         (txtDenominationNf2.Text) = record.Denomination.ToString();
            //     }
            //     record = records.FirstOrDefault(p => p.CassetteNo == "NF3");
            //     if (record != null)
            //     {
            //         (txtStackNf3.Text) = record.Count.ToString();
            //         (txtDenominationNf3.Text) = record.Denomination.ToString();
            //     }
            // }
        }

        private void GoBack(object sender, RoutedEventArgs e)
        {
            WelcomeToMobileWallet window = new WelcomeToMobileWallet();
            window.Show();
            this.Close();
        }
    }
}