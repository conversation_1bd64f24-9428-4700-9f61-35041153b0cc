﻿// Decompiled with JetBrains decompiler
// Type: MPOST.CalibrateProgressEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("05E83FCD-B177-4be8-AEB2-992F5433B835")]
  [ComVisible(true)]
  public delegate void CalibrateProgressEventHandler(object sender, EventArgs e);
}
