﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <system.diagnostics>
    <trace autoflush="true" indentsize="4">
      <listeners>
        <add name="myListener" type="System.Diagnostics.TextWriterTraceListener" initializeData="application.log" />
        <remove name="Default" />
      </listeners>
    </trace>
  </system.diagnostics>
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler,log4net" />
  </configSections>
  <log4net>
    <appender name="TestAppender" type="log4net.Appender.RollingFileAppender">
      <!--<file value="D:\Mlogs.log" />-->
      <file value="D:\MyWork\MobileWalletDesk\ErrorLogs\Nlogs.txt" />
      <encoding value="utf-8" />
      <appendToFile value="true" />
      <rollingStyle value="Date" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date- %message%n" />
      </layout>
    </appender>
    <root>
      <level value="All" />
      <!-- If the following line is not included the log file will not be created even if log4net is configured with this file. -->
      <appender-ref ref="TestAppender" />
    </root>
  </log4net> 
</configuration>