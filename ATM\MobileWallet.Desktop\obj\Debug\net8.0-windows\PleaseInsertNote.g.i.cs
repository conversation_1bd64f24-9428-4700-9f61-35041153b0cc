﻿#pragma checksum "..\..\..\PleaseInsertNote.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "78AF5D967D43937C94F6D20693BC964665E1B9F6"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MobileWallet.Desktop {
    
    
    /// <summary>
    /// PleaseInsertNote
    /// </summary>
    public partial class PleaseInsertNote : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 16 "..\..\..\PleaseInsertNote.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RowDefinition DynamicRow;
        
        #line default
        #line hidden
        
        
        #line 25 "..\..\..\PleaseInsertNote.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtInsertNote;
        
        #line default
        #line hidden
        
        
        #line 27 "..\..\..\PleaseInsertNote.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtEscrow;
        
        #line default
        #line hidden
        
        
        #line 31 "..\..\..\PleaseInsertNote.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Submit;
        
        #line default
        #line hidden
        
        
        #line 34 "..\..\..\PleaseInsertNote.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border BorderHelp;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\PleaseInsertNote.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnHelp;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\PleaseInsertNote.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border BackBorder;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\PleaseInsertNote.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Back;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\PleaseInsertNote.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border CancelBorder;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\..\PleaseInsertNote.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Cancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MobileWallet.Desktop;V1.0.0.0;component/pleaseinsertnote.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\PleaseInsertNote.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 8 "..\..\..\PleaseInsertNote.xaml"
            ((MobileWallet.Desktop.PleaseInsertNote)(target)).Closing += new System.ComponentModel.CancelEventHandler(this.PleaseInsertNote_OnClosing);
            
            #line default
            #line hidden
            
            #line 9 "..\..\..\PleaseInsertNote.xaml"
            ((MobileWallet.Desktop.PleaseInsertNote)(target)).Loaded += new System.Windows.RoutedEventHandler(this.OnWindowLoad);
            
            #line default
            #line hidden
            return;
            case 2:
            this.DynamicRow = ((System.Windows.Controls.RowDefinition)(target));
            return;
            case 3:
            this.TxtInsertNote = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TxtEscrow = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.Submit = ((System.Windows.Controls.Button)(target));
            
            #line 32 "..\..\..\PleaseInsertNote.xaml"
            this.Submit.Click += new System.Windows.RoutedEventHandler(this.Button_DepositSubmit);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BorderHelp = ((System.Windows.Controls.Border)(target));
            return;
            case 7:
            this.BtnHelp = ((System.Windows.Controls.Button)(target));
            
            #line 36 "..\..\..\PleaseInsertNote.xaml"
            this.BtnHelp.Click += new System.Windows.RoutedEventHandler(this.Button_AddMore);
            
            #line default
            #line hidden
            return;
            case 8:
            this.BackBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 9:
            this.Back = ((System.Windows.Controls.Button)(target));
            
            #line 42 "..\..\..\PleaseInsertNote.xaml"
            this.Back.Click += new System.Windows.RoutedEventHandler(this.Button_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.CancelBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 11:
            this.Cancel = ((System.Windows.Controls.Button)(target));
            
            #line 46 "..\..\..\PleaseInsertNote.xaml"
            this.Cancel.Click += new System.Windows.RoutedEventHandler(this.Button_Click_2);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

