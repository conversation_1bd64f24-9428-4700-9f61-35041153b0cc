using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace MobileWallet.Desktop.Client
{
    /// <summary>
    /// Extension methods for HttpClient to provide additional resilience features
    /// </summary>
    public static class HttpClientExtensions
    {
        /// <summary>
        /// Sends an HTTP request with automatic retry and connection health monitoring
        /// </summary>
        /// <param name="httpClient">The HttpClient instance</param>
        /// <param name="request">The HTTP request message</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>HTTP response message</returns>
        public static async Task<HttpResponseMessage> SendWithResilienceAsync(
            this HttpClient httpClient,
            HttpRequestMessage request,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var response = await httpClient.SendAsync(request, cancellationToken);
                
                // Record successful connection if response is successful
                if (response.IsSuccessStatusCode)
                {
                    ConnectionHealthMonitor.RecordSuccessfulConnection();
                }
                
                return response;
            }
            catch (Exception ex)
            {
                // Record connection failure
                ConnectionHealthMonitor.RecordConnectionFailure();
                
                // Log the error with connection status
                var connectionStatus = await ConnectionHealthMonitor.GetConnectionStatusAsync();
                App.AppLogger?.Error(ex, $"HTTP request failed. Connection status: {connectionStatus}");
                
                throw;
            }
        }

        /// <summary>
        /// Checks if the API is reachable before making a request
        /// </summary>
        /// <param name="httpClient">The HttpClient instance</param>
        /// <param name="timeout">Timeout for the connectivity check</param>
        /// <returns>True if the API is reachable</returns>
        public static async Task<bool> IsApiReachableAsync(this HttpClient httpClient, TimeSpan? timeout = null)
        {
            var actualTimeout = timeout ?? TimeSpan.FromSeconds(10);
            
            try
            {
                using var cts = new CancellationTokenSource(actualTimeout);
                using var request = new HttpRequestMessage(HttpMethod.Head, "/health");
                
                var response = await httpClient.SendAsync(request, cts.Token);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                // If health endpoint doesn't exist, try ping
                return await ConnectionHealthMonitor.PingApiHostAsync((int)actualTimeout.TotalMilliseconds);
            }
        }

        /// <summary>
        /// Waits for API connectivity to be restored before proceeding
        /// </summary>
        /// <param name="httpClient">The HttpClient instance</param>
        /// <param name="maxWaitTime">Maximum time to wait for connectivity</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if connectivity is restored</returns>
        public static async Task<bool> WaitForConnectivityAsync(
            this HttpClient httpClient,
            TimeSpan maxWaitTime,
            CancellationToken cancellationToken = default)
        {
            return await ConnectionHealthMonitor.WaitForConnectivityAsync(maxWaitTime, cancellationToken);
        }
    }
}
