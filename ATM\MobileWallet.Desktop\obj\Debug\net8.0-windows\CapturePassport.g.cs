﻿#pragma checksum "..\..\..\CapturePassport.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "396AE15255336B871FFC4CA936EDFB3DF1E071C7"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MobileWallet.Desktop {
    
    
    /// <summary>
    /// CapturePassport
    /// </summary>
    public partial class CapturePassport : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 15 "..\..\..\CapturePassport.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RowDefinition DynamicRow;
        
        #line default
        #line hidden
        
        
        #line 29 "..\..\..\CapturePassport.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WindowTitle;
        
        #line default
        #line hidden
        
        
        #line 34 "..\..\..\CapturePassport.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnBack;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\CapturePassport.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCancel;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\CapturePassport.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border CaptureBorder;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\CapturePassport.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCapture;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\CapturePassport.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border SubmitBorder;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\CapturePassport.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSubmit;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\CapturePassport.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtPassportDetails;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MobileWalletDesk;component/capturepassport.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\CapturePassport.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 8 "..\..\..\CapturePassport.xaml"
            ((MobileWallet.Desktop.CapturePassport)(target)).Loaded += new System.Windows.RoutedEventHandler(this.CapturePassport_OnLoaded);
            
            #line default
            #line hidden
            
            #line 9 "..\..\..\CapturePassport.xaml"
            ((MobileWallet.Desktop.CapturePassport)(target)).Closing += new System.ComponentModel.CancelEventHandler(this.CapturePassport_OnClosing);
            
            #line default
            #line hidden
            return;
            case 2:
            this.DynamicRow = ((System.Windows.Controls.RowDefinition)(target));
            return;
            case 3:
            this.WindowTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.BtnBack = ((System.Windows.Controls.Button)(target));
            
            #line 35 "..\..\..\CapturePassport.xaml"
            this.BtnBack.Click += new System.Windows.RoutedEventHandler(this.Button_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BtnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 39 "..\..\..\CapturePassport.xaml"
            this.BtnCancel.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Cancel);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CaptureBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 7:
            this.BtnCapture = ((System.Windows.Controls.Button)(target));
            
            #line 45 "..\..\..\CapturePassport.xaml"
            this.BtnCapture.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Capture);
            
            #line default
            #line hidden
            return;
            case 8:
            this.SubmitBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 9:
            this.BtnSubmit = ((System.Windows.Controls.Button)(target));
            
            #line 49 "..\..\..\CapturePassport.xaml"
            this.BtnSubmit.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Submit);
            
            #line default
            #line hidden
            return;
            case 10:
            this.TxtPassportDetails = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

