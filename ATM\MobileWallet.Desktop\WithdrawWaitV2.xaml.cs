﻿using System.ComponentModel;
using System.Windows;
using System.Windows.Media.Imaging;
using ITL;
using ITL.Events;
using Microsoft.IdentityModel.Logging;
using MobileWallet.Desktop.API;
using MobileWallet.Desktop.Atm;
using MobileWallet.Desktop.Client;
using MobileWallet.Desktop.Extensions;
using ZXing;

namespace MobileWallet.Desktop;

public partial class WithdrawWaitV2 : CashDeviceWindow
{
    private AppTransactionDto result;
    private string accountNumber;
    private int amount;
    private GetCryptoQuoteResponseModel data;
    private string amountToSend = "";

    public WithdrawWaitV2(
        AppTransactionDto resultData,
        string accountNumber,
        int amount,
        GetCryptoQuoteResponseModel data
    )
    {
        InitializeComponent();
        Set_Language();
        this.loaderImage.Source = new Uri(Environment.CurrentDirectory + @"/images/loader1.gif");
        result = resultData;
        this.accountNumber = accountNumber;
        this.amount = amount;
        this.data = data;
    }

    private BitmapSource GenerateQrCode(string text)
    {
        // Initialize BarcodeWriter
        var writer = new BarcodeWriterPixelData()
        {
            Format = BarcodeFormat.QR_CODE, // Specify QR Code format
            Options = new ZXing.Common.EncodingOptions
            {
                Height = 300,
                Width = 300,
                Margin =
                    1 // Optional: Adjust margin
                ,
            },
        };

        // Generate the QR Code as a Bitmap
        var qrCodeBitmap = writer.Encode(text);
        // Convert Bitmap to BitmapSource for WPF compatibility
        return qrCodeBitmap.ToBitMapSource();
    }

    public void Set_Language()
    {
        switch (Global.DefaultLanguage)
        {
            case "English":
                TransactionProcessing.Text = ResourceEnglish.TransactionProcessing;
                break;
            case "French":
                TransactionProcessing.Text = ResourceFrench.TransactionProcessing;
                break;
        }
    }

    private async void WithdrawWait_OnLoaded(object sender, RoutedEventArgs e)
    {
        BarcodeImage.Source = GenerateQrCode(result.ReceiverAddress);
        amountToSend = data.Quote.ToString("N6");
        TxtAmountToSend.Text =
            "Please send "
            + Global.SelectedToken.Symbol
            + " "
            + amountToSend
            + " to the above Address";
        InitCashDevice();
        var cashDevice = App.CashDevice;
        cashDevice.DisableAcceptor();
        cashDevice.EnablePayout();
        cashDevice.EnablePayoutDevice();
        _ = ProcessWithdraw();
    }

    private async Task ProcessWithdraw()
    {
        try
        {
            var bitCoinClient = new CryptoClient(HttpClientSingleton.Instance);
            var isTimeOut = true;
            for (int i = 0; i < 6; i++)
            {
                await Task.Delay(30000);
                try
                {
                    _ = App.LogError("Calling GET QUOTE API", LogType.Crypto);
                    var quoteResponse = await bitCoinClient.Crypto_GetCryptoQuoteByIdAsync(
                        result.TransactionId,
                        TokenManager.SessionId
                    );
                    _ = App.LogError(
                        "Fetched GET QUOTE API with Status: " + quoteResponse.Data.Status,
                        LogType.Crypto
                    );
                    var status = quoteResponse.Data;
                    if (status.Status.ToUpper() == "SUCCESS")
                    {
                        isTimeOut = false;
                        break;
                    }
                    if (status.Status.ToUpper() == "FAILED")
                    {
                        App.HideProcessingDialog();
                        CustomMessageBox.Show("Transaction Failed");
                        WelcomeToMobileWallet welcomeToMobileWallet = new WelcomeToMobileWallet();
                        welcomeToMobileWallet.Show();
                        Close();
                        return;
                    }
                }
                catch (Exception e)
                {
                     App.AppLogger.Error(e,e.Message);
                    Console.WriteLine(e);
                    _ = App.LogError(e.Message);
                }
            }

            if (isTimeOut)
            {
                App.HideProcessingDialog();
                CustomMessageBox.Show(
                    "Transaction was not completed, please pay within 30 minutes or try again after few minutes"
                );
                var isValid = ReceiptPrinter.IsValid();
                if (isValid == 0)
                {
                    double cashIn = amount;
                    string transactionId = result.TransactionId;
                    string date = DateTime.Now.ToString("yyyy-MM-dd");
                    string time = DateTime.Now.ToString("hh:mm tt");
                    string currency = Global.Currency;
                    string accountNo = accountNumber;
                    bool isSuccess = ReceiptPrinter.PrintWithdrawReceiptCrypto(
                        TokenManager.UserName,
                        Global.SelectedPassPortV2?.tPasInfo.GivenName ?? "",
                        cashIn,
                        transactionId,
                        date,
                        time,
                        currency,
                        accountNo,
                        data.Quote.ToString("N6"),
                        Global.SelectedToken.Symbol,
                        data.GasFee.ToString("N4"),
                        data.Quote.ToString("N6"),
                        result.ReceiverAddress,
                        Global.UserAddress,
                        result.TxHash,
                        null,
                        $"1 {Global.SelectedNetwork.Symbol} = $" + data.AvgPrice.ToString("F4"),
                        "1 USD = " + data.ExchangeRate + " " + Global.Currency,
                        "$" + data.TotalUsd,
                        "PENDING",
                        result.ExternalTransactionId
                    );
                }

                WelcomeToMobileWallet welcomeToMobileWallet = new WelcomeToMobileWallet();
                welcomeToMobileWallet.Show();
                Close();
                return;
            }
            _ = App.LogError("Calling Dispense", LogType.Crypto);
            var dispense = App.CashDevice.DispenseValue(
                new ValueCountryCode((uint)amount * 100, Global.Currency)
            );
            _ = App.LogError($"Called Dispense with {dispense.ToString()}", LogType.Crypto);
            if (dispense == DispenseTransactionRequestResult.OK)
            {
                //Do Nothing and wait for Events via Callback
            }
            else
            {
                switch (dispense)
                {
                    case DispenseTransactionRequestResult.NOT_SUPPORTED:
                        CustomMessageBox.ShowDialog("This function is not supported.");
                        break;
                    case DispenseTransactionRequestResult.BUSY:
                        CustomMessageBox.ShowDialog(
                            "The dispenser is currently busy. Please try again later."
                        );
                        break;
                    case DispenseTransactionRequestResult.INVALID_INPUT:
                        CustomMessageBox.ShowDialog(
                            "Invalid input provided. Please check the transaction details."
                        );
                        break;
                    case DispenseTransactionRequestResult.NOT_ENOUGH_VALUE:
                        CustomMessageBox.ShowDialog(
                            "Insufficient funds or value to complete the transaction."
                        );
                        break;
                    case DispenseTransactionRequestResult.CANNOT_PAY_EXACT_AMOUNT:
                        CustomMessageBox.ShowDialog(
                            "Unable to dispense the exact amount requested."
                        );
                        break;
                    case DispenseTransactionRequestResult.MULTI_CURRENCY_MODE:
                        CustomMessageBox.ShowDialog(
                            "Multi-currency mode is enabled. Dispensing is not available in this mode."
                        );
                        break;
                }
            }
        }
        catch (Exception e)
        {
                App.AppLogger.Error(e,e.Message);
            CustomMessageBox.Show("Transaction was not completed, due to some error");
            var isValid = ReceiptPrinter.IsValid();
            if (isValid == 0)
            {
                double cashIn = amount;
                string transactionId = result.TransactionId;
                string date = DateTime.Now.ToString("yyyy-MM-dd");
                string time = DateTime.Now.ToString("hh:mm tt");
                string currency = Global.Currency;
                string accountNo = accountNumber;
                bool isSuccess = ReceiptPrinter.PrintWithdrawReceiptCrypto(
                    TokenManager.UserName,
                    Global.SelectedPassPortV2?.tPasInfo.GivenName ?? "",
                    cashIn,
                    transactionId,
                    date,
                    time,
                    currency,
                    accountNo,
                    data.Quote.ToString("N6"),
                    Global.SelectedToken.Symbol,
                    data.GasFee.ToString("N4"),
                    data.Quote.ToString("N6"),
                    result.ReceiverAddress,
                    Global.UserAddress,
                    result.TxHash,
                    null,
                    $"1 {Global.SelectedNetwork.Symbol} = $" + data.AvgPrice.ToString("F4"),
                    "1 USD = " + data.ExchangeRate + " " + Global.Currency,
                    "$" + data.TotalUsd,
                    "PENDING",
                    result.ExternalTransactionId
                );
            }
            WelcomeToMobileWallet welcomeToMobileWallet = new WelcomeToMobileWallet();
            welcomeToMobileWallet.Show();
            Close();
        }
    }

    protected override void DispenserTransactionEventHandler(
        object sender,
        DispenserTransactionArgs e
    )
    {
        if (e.State == DispenserTransactionState.COMPLETED)
        {
            _ = ContinueWithPrint();
        }
        else if (e.State == DispenserTransactionState.ERROR)
        {
            Dispatcher.Invoke(() =>
            {
                App.HideProcessingDialog();
                switch (e.Data.ErrorReason)
                {
                    case DispenserTransactionErrorReason.BUSY:
                        CustomMessageBox.Show(
                            "Transaction was not completed due to the system being busy"
                        );
                        break;
                    case DispenserTransactionErrorReason.NOT_ENOUGH_VALUE:
                    case DispenserTransactionErrorReason.CANNOT_PAY_EXACT_AMOUNT:
                        CustomMessageBox.Show(
                            "Transaction was not completed due to not having enough money"
                        );
                        break;
                    case DispenserTransactionErrorReason.DEVICE_DISABLED:
                        CustomMessageBox.Show(
                            "Transaction was not completed due to device being disabled"
                        );
                        break;
                    case DispenserTransactionErrorReason.ERROR_DURING_PAYOUT:
                        CustomMessageBox.Show("Transaction was not completed due to payout error");
                        break;
                    case DispenserTransactionErrorReason.JAMMED:
                        CustomMessageBox.Show("Transaction was not completed due to Jam");
                        break;
                    case DispenserTransactionErrorReason.HALTED:
                        CustomMessageBox.Show("Transaction was not completed due to Halted");
                        break;
                    case DispenserTransactionErrorReason.UNKNOWN:
                        break;
                    case DispenserTransactionErrorReason.TIME_OUT:
                        CustomMessageBox.Show("Transaction was not completed due to timeout error");
                        break;
                }
            });
        }
    }

    private async Task ContinueWithPrint()
    {
        try
        {
            var client = new DenominationClient(HttpClientSingleton.Instance);
            await client.Denomination_SaveDenominationsAsync(
                new SaveDenominationRecordsRequestModel()
                {
                    Records = App
                        .CashDevice.currencyAssignment.Select(p => new DenominationRecordDto()
                        {
                            Count = (int)p.StoredInCashbox,
                            Denomination = (int)p.Value,
                            CassetteNo = p.Channel.ToString(),
                            LowCount = 10,
                            StopCount = 10,
                        })
                        .ToList(),
                }
            );
            double cashIn = amount;
            string transactionId = result.TransactionId;
            string date = DateTime.Now.ToString("yyyy-MM-dd");
            string time = DateTime.Now.ToString("hh:mm tt");
            string currency = Global.Currency;
            string accountNo = accountNumber;
            var isValid = ReceiptPrinter.IsValid();
            _ = App.TrackAtmRealTime(
                new UpdateAtmRealTimeRequestModel() { PrintStatus = isValid.ToString() }
            );
            if (Global.UseHardware && isValid == 0)
            {
                Dispatcher.Invoke(() =>
                {
                    bool isSuccess = ReceiptPrinter.PrintWithdrawReceiptCrypto(
                        TokenManager.UserName,
                        Global.SelectedPassPortV2?.tPasInfo.GivenName ?? "",
                        cashIn,
                        transactionId,
                        date,
                        time,
                        currency,
                        accountNo,
                        data.Quote.ToString("N6"),
                        Global.SelectedToken.Symbol,
                        data.GasFee.ToString("N4"),
                        data.Quote.ToString("N6"),
                        result.ReceiverAddress,
                        Global.UserAddress,
                        result.TxHash,
                        null,
                        $"1 {Global.SelectedNetwork.Symbol} = $" + data.AvgPrice.ToString("F4"),
                        "1 USD = " + data.ExchangeRate + " " + Global.Currency,
                        "$" + data.TotalUsd,
                        "CONFIRMED",
                        result.ExternalTransactionId
                    );
                });

                _ = App.LogError("Withdraw Print Completed", LogType.Withdraw, "");
            }
            else if (Global.UseHardware)
            {
                _ = App.LogError("Printer Error", LogType.Error, "Error Code: " + isValid);
                CustomMessageBox.Show("Receipt is not available at the moment");
            }

            if (true)
            {
                var bitCoinClient = new CryptoClient(HttpClientSingleton.Instance);
                await bitCoinClient.Crypto_RedeemQuoteAsync(result.TransactionId);
                App.HideProcessingDialog();
                // CustomMessageBox.ShowDialog(
                //     Global.IsFrench
                //         ? "Veuillez recuperer vos billets !!"
                //         : "Please collect your money!!"
                // );
                Dispatcher.Invoke(() =>
                {
                    ThankYou thankYou = new ThankYou();
                    thankYou.Show();
                    Close();
                });
            }
        }
        catch (Exception e)
        {
                App.AppLogger.Error(e,e.Message);
            App.HideProcessingDialog();
            Console.WriteLine(e);
        }
    }

    private void WithdrawWaitV2_OnClosing(object? sender, CancelEventArgs e)
    {
        UnInitCashDevice();
    }
}
