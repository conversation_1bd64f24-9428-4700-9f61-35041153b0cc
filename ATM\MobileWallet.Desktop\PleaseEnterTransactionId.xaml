﻿<Window x:Class="MobileWallet.Desktop.PleaseEnterTransactionId"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        mc:Ignorable="d"
        WindowStyle="None"
        Closing="PleaseEnterTransactionId_OnClosing"
        Loaded="PleaseEnterTransactionId_OnLoaded"
        Title="PleaseEnterTransactionId"
        Height="1600" Width="900"
        WindowState="Maximized">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="150"/>
            <RowDefinition Height="110"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Name="DynamicRow" Height="300"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>



        <Image Grid.Column="0" Grid.Row="0" Grid.RowSpan="5" Grid.ColumnSpan="3" Stretch="Fill" Source="./images/back-drop.png "/>
        <Image Grid.Row="0"  Grid.ColumnSpan="3" Width="250" Source="./images/logo.png" />

        <TextBlock Name="WindowTitle" HorizontalAlignment="Center" TextAlignment="Center" Grid.ColumnSpan="3" Grid.Row="1" Margin="0 10 0 0" FontWeight="Bold" Foreground="#ffffff" FontSize="40" TextWrapping="Wrap" Text="Place Your QR Code Align With The Scanner" />

        <StackPanel Grid.Row="3" VerticalAlignment="Center" HorizontalAlignment="Left">
            <Border Margin="20 0 0 0" HorizontalAlignment="Left"  Grid.Row="2" Grid.ColumnSpan="3" BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
                <Button Name="BtnBack" Margin="0 0 0 0" BorderThickness="0" Background="Transparent" FontWeight="DemiBold" Foreground="#5387FC" FontSize="35"
         Click="Button_Click"  >Back</Button>
            </Border>
            <Border Margin="20 50 0 0" HorizontalAlignment="Left" BorderBrush="#FC5353" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
                <Button Name="BtnCancel" Margin="0 0 0 0" BorderThickness="0" Background="Transparent" FontWeight="DemiBold" Foreground="#FC5353" FontSize="35"
         Click="Button_Click_Cancel"  >Cancel</Button>
            </Border>
        </StackPanel>
        <StackPanel HorizontalAlignment="Right" Grid.Row="3" Grid.Column="2" VerticalAlignment="Center">
            <Border Name="SubmitBorder" Margin="0 0 20 0" HorizontalAlignment="Right"  Grid.Row="0" Grid.RowSpan="3" Grid.ColumnSpan="3" BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
                <Button Name="BtnSubmit" BorderThickness="0" Background="Transparent" Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
         Height="72" VerticalAlignment="Top" Click="Button_Click_Submit"  >Submit</Button>
            </Border>
        </StackPanel>

        <StackPanel Orientation="Vertical" VerticalAlignment="Top" Grid.Row="2" HorizontalAlignment="Center" Grid.Column="0" Grid.ColumnSpan="3">
            <!-- <Image Source="/images/scan.png" Margin="0,5" Width="300" /> -->
            <Image x:Name="CameraImage" Stretch="Fill" Width="500" Height="250"/>
            <TextBox Name="TxtTransactionId" HorizontalAlignment="Center" Height="60" Width="650" Margin="0,40"   FontSize="40" MaxLength="32" FontWeight="Bold" />
        </StackPanel>

    </Grid>
</Window>
