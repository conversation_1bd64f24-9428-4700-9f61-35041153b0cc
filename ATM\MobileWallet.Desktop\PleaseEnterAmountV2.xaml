﻿<local:CashDeviceWindow x:Class="MobileWallet.Desktop.PleaseEnterAmountV2"
                  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                  xmlns:local="clr-namespace:MobileWallet.Desktop"
                  mc:Ignorable="d"
                  WindowStyle="None"
                  Loaded="OnWindowLoad"
                  Closing="OnWindowClose"
                  Title="PleaseEnterAmountV2" Height="1600" Width="900" WindowState="Maximized">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="110"/>
            <RowDefinition Height="150"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="110"/>
            <RowDefinition Height="110"/>
            <RowDefinition Height="110"/>
            <RowDefinition Height="110"/>
            <RowDefinition Height="110"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <Image Grid.Column="0" Grid.Row="0" Grid.RowSpan="9" Stretch="Fill" Source="./images/back-drop.png "/>
        <Image Grid.Row="0" Width="250" Source="./images/logo.png" />

        <TextBlock 
            TextAlignment="Center"
            Width="600"
            Name="EnterAmount" HorizontalAlignment="Center" Grid.Row="1" Margin="20 20 20 0" FontWeight="Bold" Foreground="#ffffff" FontSize="50" TextWrapping="Wrap" Text="Please Enter Amount (between $5 and $5,000)" />
        
        <Border 
            VerticalAlignment="Top"
            Margin="0 0 20 0" HorizontalAlignment="Right" Grid.Row="5" BorderBrush="#5387FC" BorderThickness="4" Width="320" Background="#ffffff" Height="80" CornerRadius="10" >
            <Button Name="xaf5000" Margin="0 0 0 0" BorderThickness="0" Background="Transparent" Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                    Click="xaf5000_Click"   > $5</Button>
        </Border>
        <Border 
            VerticalAlignment="Top"
            Margin="0 0 20 0" HorizontalAlignment="Right"  Grid.Row="6" BorderBrush="#5387FC" BorderThickness="4" Width="320" Background="#ffffff" Height="80" CornerRadius="10" >
            <Button Name="xaf10000" Margin="0 0 0 0" BorderThickness="0" Background="Transparent"  Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                    Click="xaf10000_Click"  >$10</Button>
        </Border>
        <Border 
            VerticalAlignment="Top"
            Margin="0 0 20 0" HorizontalAlignment="Right"  Grid.Row="7" BorderBrush="#5387FC" BorderThickness="4" Width="320" Background="#ffffff" Height="80" CornerRadius="10" >
            <Button Name="xaf25000" Margin="0 0 0 0" BorderThickness="0" Background="Transparent"  Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                    Click="xaf25000_Click"  >$20</Button>
        </Border>
        <Border 
            VerticalAlignment="Top"
            Margin="15 0 15 0" HorizontalAlignment="Right"  Grid.Row="8" BorderBrush="#5387FC" BorderThickness="4" Width="320" Background="#FF40C0FB" Height="80" CornerRadius="10" >
            <Button Margin="0 0 0 0" Name="Submit" BorderThickness="0" Background="Transparent" FontWeight="DemiBold" Foreground="White" FontSize="35"
                    Click="Button_Click_Submit"  >Submit</Button>
        </Border>

        <Border 
            VerticalAlignment="Top"
            Margin="0 0 20 0" HorizontalAlignment="Left"  Grid.Row="5" BorderBrush="#5387FC" BorderThickness="4" Width="320" Background="#ffffff" Height="80" CornerRadius="10" >
            <Button  x:Name="xaf100000" Margin="0 0 0 0" BorderThickness="0" Background="Transparent" FontWeight="DemiBold" Foreground="#5387FC" FontSize="35"
                     Click="xaf100000_Click"   >$50</Button>
        </Border>
        <Border 
            VerticalAlignment="Top"
            Margin="0 0 20 0" HorizontalAlignment="Left"  Grid.Row="6" BorderBrush="#5387FC" BorderThickness="4" Width="320" Background="#ffffff" Height="80" CornerRadius="10" >
            <Button  x:Name="xaf150000" Margin="0 0 0 0" BorderThickness="0" Background="Transparent" FontWeight="DemiBold" Foreground="#5387FC" FontSize="35"
                     Click="xaf150000_Click"   >$100</Button>
        </Border>
        <Border 
            VerticalAlignment="Top"
            Margin="0 0 20 0" HorizontalAlignment="Left"  Grid.Row="7" BorderBrush="#5387FC" BorderThickness="4" Width="320" Background="#ffffff" Height="80" CornerRadius="10" >
            <Button  x:Name="xaf200000" Margin="0 0 0 0" BorderThickness="0" Background="Transparent" FontWeight="DemiBold" Foreground="#5387FC" FontSize="35"
                     Click="xaf200000_Click"   >$500</Button>
        </Border>
        <Border 
            VerticalAlignment="Top"
            Margin="0 0 15 0" HorizontalAlignment="Left"  Grid.Row="8" BorderBrush="#FC5353" BorderThickness="4" Width="320" Background="#ffffff" Height="80" CornerRadius="10" >
            <Button Margin="0 0 0 0" Name="Cancel" BorderThickness="0" Background="Transparent" FontWeight="DemiBold" Foreground="#FC5353" FontSize="35"
                    Click="Button_Click_Cancel" >Cancel</Button>
        </Border>
        <StackPanel Margin="0,0,0,0" Grid.Row="2" Grid.RowSpan="7" Orientation="Vertical">
            <Label Name="lblMsgEnterAmount"  FontSize="20" FontWeight="SemiBold" Foreground="Red" Margin="280,20,0,0" />
            <TextBox HorizontalAlignment="Center" Height="60" IsReadOnly="True" Width="300" Margin="0,40"   FontSize="40" FontWeight="Bold" Name="txtAmount" />
            <Grid HorizontalAlignment="Center" Name="numbperpad" >
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition />
                    <ColumnDefinition />
                    <ColumnDefinition />
                </Grid.ColumnDefinitions>

                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>

                <Button Content="1" Grid.Column="0" Grid.Row="0" Height="100" Width="100" Background="Blue" Foreground="White" FontSize="55" Name="btn1" Click="btn1_Click" />
                <Button Content="2" Grid.Column="1" Grid.Row="0" Height="100" Width="100" Background="Blue" Foreground="White" FontSize="55" Name="btn2" Click="btn2_Click" />
                <Button Content="3" Grid.Column="2" Grid.Row="0" Height="100" Width="100" Background="Blue" Foreground="White" FontSize="55" Name="btn3" Click="btn3_Click" />

                <Button Content="4" Grid.Column="0" Grid.Row="1" Height="100" Width="100" Background="Blue" Foreground="White" FontSize="55" Name="btn4" Click="btn4_Click" />
                <Button Content="5" Grid.Column="1" Grid.Row="1" Height="100" Width="100" Background="Blue" Foreground="White" FontSize="55" Name="btn5" Click="btn5_Click" />
                <Button Content="6" Grid.Column="2" Grid.Row="1" Height="100" Width="100" Background="Blue" Foreground="White" FontSize="55" Name="btn6" Click="btn6_Click" />

                <Button Content="7" Grid.Column="0" Grid.Row="2" Height="100" Width="100" Background="Blue" Foreground="White" FontSize="55" Name="btn7" Click="btn7_Click" />
                <Button Content="8" Grid.Column="1" Grid.Row="2" Height="100" Width="100" Background="Blue" Foreground="White" FontSize="55" Name="btn8" Click="btn8_Click" />
                <Button Content="9" Grid.Column="2" Grid.Row="2" Height="100" Width="100" Background="Blue" Foreground="White" FontSize="55" Name="btn9" Click="btn9_Click" />

                <Button Content="Clear"  Grid.Column="0" Grid.Row="3" Height="100" Width="100" FontSize="20" Name="btnclear" VerticalAlignment="Top" Click="btnclear_Click_1" BorderBrush="#FFD2E231" >
                    <Button.Background>
                        <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                            <GradientStop Color="#FF373820" Offset="0.779"/>
                            <GradientStop Color="#FFD4DC2E"/>
                        </LinearGradientBrush>
                    </Button.Background>
                </Button>
                <Button Content="0" Background="Blue" Foreground="White" Grid.Column="1" Grid.Row="3" Width="100" FontSize="55" Name="btn0" Click="btn0_Click_1"  />
                <Button Content="Backspace" Grid.Column="2" Grid.Row="3" Height="100" Width="100" FontSize="20" Name="btndone" VerticalAlignment="Top" Click="btndone_Click_1"  >
                    <Button.Background>
                        <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                            <GradientStop Color="#FF0D656E" Offset="0.771"/>
                            <GradientStop Color="#FF0C8198"/>
                        </LinearGradientBrush>
                    </Button.Background>
                </Button>



            </Grid>
        </StackPanel>




    </Grid>
</local:CashDeviceWindow>
