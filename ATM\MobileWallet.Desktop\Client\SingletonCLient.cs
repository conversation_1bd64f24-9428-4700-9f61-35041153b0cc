﻿using System.IdentityModel.Tokens.Jwt;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text.Json;
using Microsoft.IdentityModel.Tokens;
using MobileWallet.Desktop.API;
using Newtonsoft.Json;
using JsonSerializer = Newtonsoft.Json.JsonSerializer;

namespace MobileWallet.Desktop.Client;

public class TokenResponse
{
    [JsonProperty("access_token")]
    public string AccessToken { get; set; }

    [JsonProperty("refresh_token")]
    public string RefreshToken { get; set; }

    [JsonProperty("id_token")]
    public string IdToken { get; set; }

    [JsonProperty("expires_in")]
    public int ExpiresIn { get; set; }
}

public static class TokenManager
{
    public static string AccessToken { get; set; } = "";
    public static string RefreshToken { get; set; } = "";
    public static DateTime Expiry { get; set; } = DateTime.Now;
    public static string IdToken { get; set; } = "";
    public static string UserId { get; set; } = "";
    public static string UserName { get; set; } = "";
    public static string SessionId { get; set; } = "";
}

public static class HttpClientSingleton
{
    private static HttpClient? _httpClient;
    private static readonly object LOCK = new object();
    public static HttpClient Instance
    {
        get
        {
            if (_httpClient == null)
            {
                lock (LOCK)
                {
                    if (_httpClient == null)
                    {
                        var handler = new AuthenticatedHttpClientHandler();
                        _httpClient = new HttpClient(handler);
                        _httpClient.Timeout = TimeSpan.FromSeconds(60); // Increased timeout
                        _httpClient.BaseAddress = new Uri(Global.BaseUrl);
                    }
                }
            }
            return _httpClient;
        }
    }
}

public class AppAuthClient
{
    public async Task<bool> Authenticate(string userName, string password, string otp = "")
    {
        try
        {
            if (string.IsNullOrEmpty(otp) && Global.UseOtp)
            {
                var client = new SmsClient(HttpClientSingleton.Instance);
                var result = await client.Sms_SendVerificationSmsForLoginAsync(
                    new SendVerificationSmsForLoginRequestModel()
                    {
                        Password = password,
                        Role = "ATM",
                        Username = userName,
                    }
                );
                if (result != null)
                {
                    return true;
                }
            }
            var request = new HttpRequestMessage(HttpMethod.Post, Global.BaseUrl + "connect/token");
            var collection = new List<KeyValuePair<string, string>>
            {
                new("username", userName),
                new("granttype", "password"),
                new("otp", otp),
                new("password", password),
                new("grant_type", "password"),
                new("scope", "openid email profile offline_access roles"),
                new("role", "ATM"),
            };
            if (!Global.UseOtp)
            {
                collection.RemoveAt(2);
            }
            var content = new FormUrlEncodedContent(collection);
            request.Content = content;
            // The retry logic is now handled by AuthenticatedHttpClientHandler
            var response = await HttpClientSingleton.Instance.SendAsync(request);
            var resultJson = (await response.Content.ReadAsStringAsync());
            if (response.IsSuccessStatusCode)
            {
                var tokeObj = JsonConvert.DeserializeObject<TokenResponse>(resultJson);
                if (tokeObj == null)
                {
                    App.AppLogger?.Error("Failed to deserialize token response");
                    return false;
                }
                TokenManager.Expiry = DateTime.Now.AddSeconds(tokeObj.ExpiresIn - 120);
                TokenManager.AccessToken = tokeObj.AccessToken;
                TokenManager.IdToken = tokeObj.IdToken;
                TokenManager.RefreshToken = tokeObj.RefreshToken;
                TokenManager.UserId =
                    JwtDecoder
                        .DecodeJwt(TokenManager.IdToken)
                        .FirstOrDefault(p => p.Type == "userId")
                        ?.Value ?? "";
                TokenManager.UserName =
                    JwtDecoder
                        .DecodeJwt(TokenManager.IdToken)
                        .FirstOrDefault(p => p.Type == "sub")
                        ?.Value ?? "";
                App.AppLogger?.Info($"Authentication successful for user: {userName}");
                return true;
            }

            App.AppLogger?.Error($"Authentication failed with status code: {response.StatusCode}");
            return false;
        }
        catch (Exception e)
        {
            App.AppLogger?.Error(e, $"Authentication error for user {userName}: {e.Message}");
            return false;
        }
    }

    public async Task<bool> RefreshToken(string refreshToken)
    {
        try
        {
            var request = new HttpRequestMessage(HttpMethod.Post, Global.BaseUrl + "connect/token");
            var collection = new List<KeyValuePair<string, string>>
            {
                new("scope", "openid email profile offline_access roles"),
                new("role", "ATM"),
                new("granttype", "refresh_token"),
                new("refresh_token", refreshToken),
                new("grant_type", "refresh_token"),
            };
            var content = new FormUrlEncodedContent(collection);
            request.Content = content;
            // The retry logic is now handled by AuthenticatedHttpClientHandler
            var response = await HttpClientSingleton.Instance.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                var resultJson = (await response.Content.ReadAsStringAsync());
                var tokeObj = JsonConvert.DeserializeObject<TokenResponse>(resultJson);
                if (tokeObj == null)
                {
                    App.AppLogger?.Error("Failed to deserialize refresh token response");
                    return false;
                }
                TokenManager.Expiry = DateTime.Now.AddSeconds(tokeObj.ExpiresIn - 120);
                TokenManager.AccessToken = tokeObj.AccessToken;
                TokenManager.RefreshToken = tokeObj.RefreshToken;
                TokenManager.IdToken = tokeObj.IdToken;
                TokenManager.UserId =
                    JwtDecoder
                        .DecodeJwt(TokenManager.IdToken)
                        .FirstOrDefault(p => p.Type == "userId")
                        ?.Value ?? "";
                TokenManager.UserName =
                    JwtDecoder
                        .DecodeJwt(TokenManager.IdToken)
                        .FirstOrDefault(p => p.Type == "sub")
                        ?.Value ?? "";
                App.AppLogger?.Info("Token refresh successful");
                return true;
            }

            App.AppLogger?.Error($"Token refresh failed with status code: {response.StatusCode}");
            return false;
        }
        catch (Exception e)
        {
            App.AppLogger.Error(e, e.Message);
            return false;
        }
    }
}

public class AuthenticatedHttpClientHandler : DelegatingHandler
{
    private const int MaxRetryAttempts = 3;
    private static readonly TimeSpan[] RetryDelays =
    {
        TimeSpan.FromSeconds(1),
        TimeSpan.FromSeconds(3),
        TimeSpan.FromSeconds(5)
    };

    public AuthenticatedHttpClientHandler()
    {
        InnerHandler = new HttpClientHandler()
        {
            // Configure connection pooling and keep-alive
            MaxConnectionsPerServer = 10,
            UseCookies = false
        };
    }

    protected override async Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request,
        CancellationToken cancellationToken
    )
    {
        // Set authorization header
        var accessToken = TokenManager.AccessToken;
        if (!string.IsNullOrWhiteSpace(accessToken))
        {
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
        }

        // Replace localhost URL with actual base URL
        request.RequestUri = new Uri(
            request.RequestUri.AbsoluteUri.Replace("https://localhost:5001/", Global.BaseUrl)
        );

        HttpResponseMessage? response = null;
        Exception? lastException = null;

        // Retry logic with exponential backoff
        for (int attempt = 0; attempt <= MaxRetryAttempts; attempt++)
        {
            try
            {
                // Clone the request for retry attempts (except the first one)
                var requestToSend = attempt == 0 ? request : await CloneHttpRequestMessageAsync(request);

                response = await base.SendAsync(requestToSend, cancellationToken);

                // Handle unauthorized responses (token refresh)
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    var isTokenRefreshed = await new AppAuthClient().RefreshToken(TokenManager.RefreshToken);
                    if (isTokenRefreshed)
                    {
                        // Update authorization header and retry
                        accessToken = TokenManager.AccessToken;
                        requestToSend.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                        response = await base.SendAsync(requestToSend, cancellationToken);
                    }
                }

                // If we get a successful response or a client error (4xx), don't retry
                if (response.IsSuccessStatusCode ||
                    ((int)response.StatusCode >= 400 && (int)response.StatusCode < 500 && response.StatusCode != HttpStatusCode.RequestTimeout))
                {
                    if (response.IsSuccessStatusCode)
                    {
                        ConnectionHealthMonitor.RecordSuccessfulConnection();
                    }
                    return response;
                }

                // For server errors (5xx) or timeout, we'll retry
                if (attempt < MaxRetryAttempts)
                {
                    App.AppLogger?.Info($"HTTP request failed with status {response.StatusCode}. Retrying in {RetryDelays[attempt].TotalSeconds} seconds. Attempt {attempt + 1}/{MaxRetryAttempts + 1}");
                    await Task.Delay(RetryDelays[attempt], cancellationToken);
                    response?.Dispose(); // Dispose the failed response
                }
            }
            catch (HttpRequestException ex) when (IsNetworkError(ex))
            {
                lastException = ex;
                ConnectionHealthMonitor.RecordConnectionFailure();
                if (attempt < MaxRetryAttempts)
                {
                    App.AppLogger?.Info($"Network error occurred: {ex.Message}. Retrying in {RetryDelays[attempt].TotalSeconds} seconds. Attempt {attempt + 1}/{MaxRetryAttempts + 1}");
                    await Task.Delay(RetryDelays[attempt], cancellationToken);
                }
                else
                {
                    App.AppLogger?.Error(ex, $"Network error after {MaxRetryAttempts + 1} attempts: {ex.Message}");
                }
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException || !cancellationToken.IsCancellationRequested)
            {
                lastException = ex;
                ConnectionHealthMonitor.RecordConnectionFailure();
                if (attempt < MaxRetryAttempts)
                {
                    App.AppLogger?.Info($"Request timeout occurred. Retrying in {RetryDelays[attempt].TotalSeconds} seconds. Attempt {attempt + 1}/{MaxRetryAttempts + 1}");
                    await Task.Delay(RetryDelays[attempt], cancellationToken);
                }
                else
                {
                    App.AppLogger?.Error(ex, $"Request timeout after {MaxRetryAttempts + 1} attempts");
                }
            }
            catch (Exception ex)
            {
                // For other exceptions, don't retry
                App.AppLogger?.Error(ex, $"Unexpected error during HTTP request: {ex.Message}");
                throw;
            }
        }

        // If we've exhausted all retries, throw the last exception or return the last response
        if (lastException != null)
        {
            throw new HttpRequestException($"Request failed after {MaxRetryAttempts + 1} attempts. Last error: {lastException.Message}", lastException);
        }

        return response ?? throw new HttpRequestException("Request failed after all retry attempts");
    }

    private static bool IsNetworkError(HttpRequestException ex)
    {
        // Check for common network-related error messages
        var message = ex.Message.ToLowerInvariant();
        return message.Contains("connection") ||
               message.Contains("timeout") ||
               message.Contains("network") ||
               message.Contains("host") ||
               message.Contains("dns") ||
               message.Contains("socket");
    }

    private static async Task<HttpRequestMessage> CloneHttpRequestMessageAsync(HttpRequestMessage original)
    {
        var clone = new HttpRequestMessage(original.Method, original.RequestUri)
        {
            Version = original.Version
        };

        // Copy headers
        foreach (var header in original.Headers)
        {
            clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
        }

        // Copy content if present
        if (original.Content != null)
        {
            var contentBytes = await original.Content.ReadAsByteArrayAsync();
            clone.Content = new ByteArrayContent(contentBytes);

            // Copy content headers
            foreach (var header in original.Content.Headers)
            {
                clone.Content.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }
        }

        return clone;
    }
}

public class JwtDecoder
{
    public static IList<Claim> DecodeJwt(string token)
    {
        var handler = new JwtSecurityTokenHandler();

        // Check if the token is valid and in the JWT format
        if (handler.CanReadToken(token))
        {
            var jwtToken = handler.ReadJwtToken(token);
            return jwtToken.Claims.ToList();
            // Decode Header
            var header = jwtToken.Header;
            Console.WriteLine("Header:");
            foreach (var item in header)
            {
                Console.WriteLine($"{item.Key}: {item.Value}");
            }

            // Decode Payload (Claims)
            var payload = jwtToken.Payload;
            Console.WriteLine("\nPayload:");
            foreach (var claim in payload)
            {
                Console.WriteLine($"{claim.Key}: {claim.Value}");
            }

            // Optionally: Serialize payload to JSON string
            string jsonPayload = JsonConvert.SerializeObject(payload);
            Console.WriteLine("\nSerialized Payload (JSON):\n" + jsonPayload);
        }
        else
        {
            Console.WriteLine("The token is not in a readable format.");
        }
        return new List<Claim>();
    }
}
