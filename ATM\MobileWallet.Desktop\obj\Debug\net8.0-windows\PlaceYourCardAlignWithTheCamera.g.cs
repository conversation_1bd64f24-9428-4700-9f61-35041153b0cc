﻿#pragma checksum "..\..\..\PlaceYourCardAlignWithTheCamera.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4C629C5228787CB2F1E8770968FBBF5C0B35E12C"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MobileWallet.Desktop {
    
    
    /// <summary>
    /// PlaceYourCardAlignWithTheCamera
    /// </summary>
    public partial class PlaceYourCardAlignWithTheCamera : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 22 "..\..\..\PlaceYourCardAlignWithTheCamera.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CameraGrid;
        
        #line default
        #line hidden
        
        
        #line 30 "..\..\..\PlaceYourCardAlignWithTheCamera.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PlaceCard;
        
        #line default
        #line hidden
        
        
        #line 34 "..\..\..\PlaceYourCardAlignWithTheCamera.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Capture;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\PlaceYourCardAlignWithTheCamera.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Cancel;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\PlaceYourCardAlignWithTheCamera.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Back;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MobileWalletDesk;component/placeyourcardalignwiththecamera.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\PlaceYourCardAlignWithTheCamera.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 8 "..\..\..\PlaceYourCardAlignWithTheCamera.xaml"
            ((MobileWallet.Desktop.PlaceYourCardAlignWithTheCamera)(target)).Loaded += new System.Windows.RoutedEventHandler(this.PlaceYourCardAlignWithTheCamera_OnLoaded);
            
            #line default
            #line hidden
            
            #line 9 "..\..\..\PlaceYourCardAlignWithTheCamera.xaml"
            ((MobileWallet.Desktop.PlaceYourCardAlignWithTheCamera)(target)).Closing += new System.ComponentModel.CancelEventHandler(this.PlaceYourCardAlignWithTheCamera_OnClosing);
            
            #line default
            #line hidden
            return;
            case 2:
            this.CameraGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 3:
            this.PlaceCard = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.Capture = ((System.Windows.Controls.Button)(target));
            
            #line 35 "..\..\..\PlaceYourCardAlignWithTheCamera.xaml"
            this.Capture.Click += new System.Windows.RoutedEventHandler(this.Button_Click_CaptureImage);
            
            #line default
            #line hidden
            return;
            case 5:
            this.Cancel = ((System.Windows.Controls.Button)(target));
            
            #line 39 "..\..\..\PlaceYourCardAlignWithTheCamera.xaml"
            this.Cancel.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Cancel);
            
            #line default
            #line hidden
            return;
            case 6:
            this.Back = ((System.Windows.Controls.Button)(target));
            
            #line 48 "..\..\..\PlaceYourCardAlignWithTheCamera.xaml"
            this.Back.Click += new System.Windows.RoutedEventHandler(this.Button_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

