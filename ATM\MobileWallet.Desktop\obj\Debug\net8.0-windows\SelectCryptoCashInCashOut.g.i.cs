﻿#pragma checksum "..\..\..\SelectCryptoCashInCashOut.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "796D958AFD380FA7FB5C40094240B6A01F69ACBA"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MobileWallet.Desktop;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MobileWallet.Desktop {
    
    
    /// <summary>
    /// SelectCryptoCashInCashOut
    /// </summary>
    public partial class SelectCryptoCashInCashOut : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 16 "..\..\..\SelectCryptoCashInCashOut.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RowDefinition DynamicRow;
        
        #line default
        #line hidden
        
        
        #line 23 "..\..\..\SelectCryptoCashInCashOut.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MyHeader;
        
        #line default
        #line hidden
        
        
        #line 26 "..\..\..\SelectCryptoCashInCashOut.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border BtnDepositBorder;
        
        #line default
        #line hidden
        
        
        #line 27 "..\..\..\SelectCryptoCashInCashOut.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnBuy;
        
        #line default
        #line hidden
        
        
        #line 30 "..\..\..\SelectCryptoCashInCashOut.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border BtnWithdrawBorder;
        
        #line default
        #line hidden
        
        
        #line 31 "..\..\..\SelectCryptoCashInCashOut.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnWithdraw;
        
        #line default
        #line hidden
        
        
        #line 34 "..\..\..\SelectCryptoCashInCashOut.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border BtnWithdrawResume;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\SelectCryptoCashInCashOut.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnResume;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\SelectCryptoCashInCashOut.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Back;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\..\SelectCryptoCashInCashOut.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Cancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MobileWallet.Desktop;V1.0.0.0;component/selectcryptocashincashout.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\SelectCryptoCashInCashOut.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 8 "..\..\..\SelectCryptoCashInCashOut.xaml"
            ((MobileWallet.Desktop.SelectCryptoCashInCashOut)(target)).Closing += new System.ComponentModel.CancelEventHandler(this.SelectCryptoCashInCashOut_OnClosing);
            
            #line default
            #line hidden
            
            #line 9 "..\..\..\SelectCryptoCashInCashOut.xaml"
            ((MobileWallet.Desktop.SelectCryptoCashInCashOut)(target)).Loaded += new System.Windows.RoutedEventHandler(this.SelectCryptoCashInCashOut_OnLoaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.DynamicRow = ((System.Windows.Controls.RowDefinition)(target));
            return;
            case 3:
            this.MyHeader = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.BtnDepositBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 5:
            this.BtnBuy = ((System.Windows.Controls.Button)(target));
            
            #line 28 "..\..\..\SelectCryptoCashInCashOut.xaml"
            this.BtnBuy.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Buy);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BtnWithdrawBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 7:
            this.BtnWithdraw = ((System.Windows.Controls.Button)(target));
            
            #line 32 "..\..\..\SelectCryptoCashInCashOut.xaml"
            this.BtnWithdraw.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Withdrawl);
            
            #line default
            #line hidden
            return;
            case 8:
            this.BtnWithdrawResume = ((System.Windows.Controls.Border)(target));
            return;
            case 9:
            this.BtnResume = ((System.Windows.Controls.Button)(target));
            
            #line 36 "..\..\..\SelectCryptoCashInCashOut.xaml"
            this.BtnResume.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Resume);
            
            #line default
            #line hidden
            return;
            case 10:
            this.Back = ((System.Windows.Controls.Button)(target));
            
            #line 42 "..\..\..\SelectCryptoCashInCashOut.xaml"
            this.Back.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Back);
            
            #line default
            #line hidden
            return;
            case 11:
            this.Cancel = ((System.Windows.Controls.Button)(target));
            
            #line 46 "..\..\..\SelectCryptoCashInCashOut.xaml"
            this.Cancel.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Cancel);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

