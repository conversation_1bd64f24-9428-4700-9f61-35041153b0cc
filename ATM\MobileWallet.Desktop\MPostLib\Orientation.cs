﻿// Decompiled with JetBrains decompiler
// Type: MPOST.Orientation
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("BB9BB62C-B6AE-4a34-8210-84340EDB1DBE")]
  [ComVisible(true)]
  public enum Orientation
  {
    RightUp,
    RightDown,
    LeftUp,
    LeftDown,
    Unknown,
  }
}
