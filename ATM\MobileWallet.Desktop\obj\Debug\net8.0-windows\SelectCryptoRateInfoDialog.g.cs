﻿#pragma checksum "..\..\..\SelectCryptoRateInfoDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C63E121D1D6AC677DB44CBB7F0FBDAB9EF7DF0EB"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MobileWallet.Desktop {
    
    
    /// <summary>
    /// SelectCryptoRateInfoDialog
    /// </summary>
    public partial class SelectCryptoRateInfoDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 23 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MyHeader;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtMarketLabel;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtMarketValue;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtAveragePriceLabel;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtAveragePriceValue;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtSlippageLabel;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtSlippageValue;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtAmountLabel;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtAmountValue;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtFeeLabel;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtFeeValue;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtQuoteLabel;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtQuoteValue;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtExchangeRate;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtExchangeRateValue;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalUsdLabel;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalUsdValue;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel BtnYes;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnOkContent;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel BtnNo;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnNoContent;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\SelectCryptoRateInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnYesContent;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MobileWalletDesk;component/selectcryptorateinfodialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\SelectCryptoRateInfoDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.MyHeader = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TxtMarketLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TxtMarketValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TxtAveragePriceLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.TxtAveragePriceValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.TxtSlippageLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TxtSlippageValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TxtAmountLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TxtAmountValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.TxtFeeLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.TxtFeeValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.TxtQuoteLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TxtQuoteValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.TxtExchangeRate = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.TxtExchangeRateValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.TxtTotalUsdLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.TxtTotalUsdValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.BtnYes = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 19:
            this.BtnOkContent = ((System.Windows.Controls.Button)(target));
            
            #line 154 "..\..\..\SelectCryptoRateInfoDialog.xaml"
            this.BtnOkContent.Click += new System.Windows.RoutedEventHandler(this.Yes_OnClick);
            
            #line default
            #line hidden
            return;
            case 20:
            this.BtnNo = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 21:
            this.BtnNoContent = ((System.Windows.Controls.Button)(target));
            
            #line 169 "..\..\..\SelectCryptoRateInfoDialog.xaml"
            this.BtnNoContent.Click += new System.Windows.RoutedEventHandler(this.No_OnClick);
            
            #line default
            #line hidden
            return;
            case 22:
            this.BtnYesContent = ((System.Windows.Controls.Button)(target));
            
            #line 178 "..\..\..\SelectCryptoRateInfoDialog.xaml"
            this.BtnYesContent.Click += new System.Windows.RoutedEventHandler(this.Yes_OnClick);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

