{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\MobileWallet\\ATM\\MobileWallet.Test\\MobileWallet.Test.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\MobileWallet\\ATM\\MobileWallet.Desktop\\MobileWallet.Desktop.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\MobileWallet\\ATM\\MobileWallet.Desktop\\MobileWallet.Desktop.csproj", "projectName": "MobileWallet.Desktop", "projectPath": "C:\\Users\\<USER>\\Desktop\\MobileWallet\\ATM\\MobileWallet.Desktop\\MobileWallet.Desktop.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\MobileWallet\\ATM\\MobileWallet.Desktop\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Emgu.CV.Bitmap": {"target": "Package", "version": "[4.9.0.5494, )"}, "Emgu.CV.runtime.windows": {"target": "Package", "version": "[4.9.0.5494, )"}, "Microsoft.AspNet.WebApi.Client": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.AspNetCore.SignalR.Client": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[8.3.0, )"}, "NLog": {"target": "Package", "version": "[6.0.2, )"}, "NLog.Schema": {"target": "Package", "version": "[6.0.2, )"}, "Python.Runtime": {"target": "Package", "version": "[2.7.9, )"}, "Python.Runtime.Windows": {"target": "Package", "version": "[3.7.2, )"}, "QRCoder": {"target": "Package", "version": "[1.6.0, )"}, "System.IO.Ports": {"target": "Package", "version": "[9.0.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.3.0, )"}, "ZXing.Net": {"target": "Package", "version": "[0.16.9, )"}, "ZXing.Net.Bindings.EmguCV": {"target": "Package", "version": "[0.16.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\MobileWallet\\ATM\\MobileWallet.Test\\MobileWallet.Test.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\MobileWallet\\ATM\\MobileWallet.Test\\MobileWallet.Test.csproj", "projectName": "MobileWallet.Test", "projectPath": "C:\\Users\\<USER>\\Desktop\\MobileWallet\\ATM\\MobileWallet.Test\\MobileWallet.Test.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\MobileWallet\\ATM\\MobileWallet.Test\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\MobileWallet\\ATM\\MobileWallet.Desktop\\MobileWallet.Desktop.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\MobileWallet\\ATM\\MobileWallet.Desktop\\MobileWallet.Desktop.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}