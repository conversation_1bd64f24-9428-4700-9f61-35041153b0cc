﻿<Window x:Class="MobileWallet.Desktop.PlaceYourCardAlignWithTheCamera"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        WindowStyle="None"
        Loaded="PlaceYourCardAlignWithTheCamera_OnLoaded"
        Closing="PlaceYourCardAlignWithTheCamera_OnClosing"
        Title="PlaceYourCardAlignWithTheCamera" Height="900" Width="1600" WindowState="Maximized"  >
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="150"/>
            <RowDefinition Height="150"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        <Grid x:Name="CameraGrid" />


        <Image Grid.Column="0" Grid.Row="0" Grid.RowSpan="3" Grid.ColumnSpan="3" Stretch="Fill" Source="./images/back-drop.png "/>
        <Image Grid.Row="0"  Grid.ColumnSpan="3" Width="250" Source="./images/logo.png" />

        <TextBlock 
            TextAlignment="Center"
            Name="PlaceCard" HorizontalAlignment="Center" Grid.ColumnSpan="3" Grid.Row="1" Margin="0 0 0 0" FontWeight="Bold" Foreground="#ffffff" FontSize="50" TextWrapping="Wrap" Text="Place Your Card Align With The Camera" />


        <Border Margin="0 180 20 0" HorizontalAlignment="Right" Grid.Row="0" Grid.RowSpan="3" Grid.ColumnSpan="3"  BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
            <Button BorderThickness="0" Name="Capture" Background="Transparent" Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                    Click="Button_Click_CaptureImage"  >Capture</Button>
        </Border>
        <Border Margin="0 430 20 0" HorizontalAlignment="Right"  Grid.Row="0" Grid.RowSpan="3" Grid.ColumnSpan="3" BorderBrush="#FC5353" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
            <Button Margin="0 0 0 0" Name="Cancel" BorderThickness="0" Background="Transparent" FontWeight="DemiBold" Foreground="#FC5353" FontSize="35"
                    Click="Button_Click_Cancel"  >Cancel</Button>
        </Border>
        <StackPanel Orientation="Vertical" VerticalAlignment="Top" Margin="0 20 0 0" Grid.Row="2" Grid.Column="1">
            <Image Source="/images/scan.png" />
            <Image Source="/images/scan.png" Margin="0,5" />
        </StackPanel>
        
        <Border Margin="0 160 20 0" HorizontalAlignment="Left"  Grid.Row="2" Grid.RowSpan="3" Grid.ColumnSpan="3" BorderBrush="#FC5353" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
            <Button Margin="0 0 0 0" Name="Back" BorderThickness="0" Background="Transparent" FontWeight="DemiBold" Foreground="#FC5353" FontSize="35"
                    Click="Button_Click"  >Back</Button>
        </Border>

    </Grid>
</Window>
