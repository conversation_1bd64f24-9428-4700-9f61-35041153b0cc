﻿<local:CashDeviceWindow x:Class="MobileWallet.Desktop.PleaseInsertNoteV2"
                  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                  xmlns:local="clr-namespace:MobileWallet.Desktop"
                  mc:Ignorable="d"
                  WindowStyle="None"
                  Closing="PleaseInsertNote_OnClosing"
                  Loaded="OnWindowLoad"
                  Title="PleaseInsertNote" Height="1600" Width="900" WindowState="Maximized">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="150"/>
            <RowDefinition Height="220"/>
            <RowDefinition Height="100"/>
            <RowDefinition Name="DynamicRow" Height="500"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <Image Grid.Column="0" Grid.Row="0" Grid.RowSpan="5" Stretch="Fill" Source="./images/back-drop.png "/>
        <Image Grid.Row="0" Width="250" Source="./images/logo.png" />
        <!--<Image Source="/images/Cash_Acceptor.jpg" Margin="0,56,0,185" Grid.Row="2" RenderTransformOrigin="0.502,0.648" />-->


        <TextBlock Name="TxtInsertNote" TextAlignment="Center" HorizontalAlignment="Center" Grid.Row="1" Margin="0 20 0 0" FontWeight="Bold" Foreground="#ffffff" FontSize="50" TextWrapping="Wrap" Text="Please Insert Note And Then Click Submit" />
        <StackPanel Grid.Row="2" VerticalAlignment="Center" HorizontalAlignment="Center">
            <TextBlock Name="TxtEscrow" HorizontalAlignment="Center" TextAlignment="Center" Grid.Row="2" Margin="0 0 0 0" FontWeight="Bold" Foreground="#ffffff" FontSize="50" TextWrapping="Wrap" Text="Deposit Amount: 0" />
        </StackPanel>
        <StackPanel Grid.Row="3" HorizontalAlignment="Right" VerticalAlignment="Center">
            <Border Margin="0 0 20 0" Grid.Row="0" Grid.RowSpan="4" BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
                <Button Margin="0 0 0 0" Name="Submit" BorderThickness="0" Background="Transparent" Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
            Click="Button_DepositSubmit"  >Submit</Button>
            </Border>
            <Border Name="BorderHelp" Margin="0 50 20 0" HorizontalAlignment="Right" Grid.Row="0" Grid.RowSpan="4" BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
                <Button Margin="0 0 0 0" Name="BtnHelp" BorderThickness="0" Background="Transparent" Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
            Click="Button_AddMore"  >Help</Button>
            </Border>
        </StackPanel>
        <StackPanel Grid.Row="3" HorizontalAlignment="Left" VerticalAlignment="Center">
            <Border Name="BackBorder" Margin="20 0 0 0" HorizontalAlignment="Left" Grid.Row="0" Grid.RowSpan="4" BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
                <Button Margin="0 0 0 0" Name="Back" BorderThickness="0" Background="Transparent" Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
            Click="Button_Click">Back</Button>
            </Border>
            <Border Name="CancelBorder" Margin="20 50 0 0" HorizontalAlignment="Left"  Grid.Row="0" Grid.RowSpan="4" BorderBrush="#FC5353" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
                <Button Margin="0 0 0 0" Name="Cancel" BorderThickness="0" Background="Transparent" FontWeight="DemiBold" Foreground="#FC5353" FontSize="35"
            Click="Button_Click_2"  >Cancel</Button>
            </Border>
        </StackPanel>
        
    </Grid>
</local:CashDeviceWindow>
