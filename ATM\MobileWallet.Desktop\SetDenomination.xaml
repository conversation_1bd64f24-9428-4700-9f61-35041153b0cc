﻿<Window x:Class="MobileWallet.Desktop.SetDenomination"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        mc:Ignorable="d"
        Loaded="SetDenomination_OnLoaded"
        WindowStyle="None"
        Title="Please Set Denominations" Height="900" Width="1600" WindowState="Maximized">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="100" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <Image Grid.Column="0" Grid.Row="0" Grid.RowSpan="2" Stretch="Fill" Source="./images/back-drop.png " />
        <Image Grid.Row="0" Width="250" Source="./images/logo.png" />
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <Label
                    Padding="10"
                    Grid.Row="0"
                    Grid.Column="1"
                    HorizontalAlignment="Center"
                    FontSize="25" FontWeight="Bold" Foreground="Red">
                    Login to Atm
                </Label>
                
                <Label
                    Padding="10"
                    Grid.Row="0"
                    Grid.Column="2"
                    HorizontalAlignment="Center"
                    FontSize="25" FontWeight="Bold" Foreground="Red">
                    Total Stack
                </Label>
                <Label
                    Padding="10"
                    Grid.Row="1"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Center"
                    FontSize="30" FontWeight="SemiBold" Foreground="Red">
                    Username
                </Label>
                <TextBox
                    GotFocus="GotFocus"
                    Grid.Row="1" Grid.Column="1"
                    HorizontalAlignment="Center" Height="60" IsReadOnly="True" Width="500" Margin="0,20"   FontSize="40" FontWeight="Bold" Name="txtDenominationNf1" />
                <TextBox
                    GotFocus="GotFocus"
                    Grid.Row="2" Grid.Column="1"
                    HorizontalAlignment="Center" Height="60" IsReadOnly="True" Width="500" Margin="0,20"   FontSize="40" FontWeight="Bold" Name="txtDenominationNf2" />
                <Label
                    Padding="10"
                    Grid.Column="0"
                    Grid.Row="2"
                    HorizontalAlignment="Center"
                    FontSize="30" FontWeight="SemiBold" Foreground="Red">
                    Pin
                </Label>
                <TextBox
                    GotFocus="GotFocus"
                    Grid.Row="2" Grid.Column="1"
                    HorizontalAlignment="Center" Height="60" IsReadOnly="True" Width="500" Margin="0,20"   FontSize="40" FontWeight="Bold" Name="txtDenominationNf3" />
              </Grid>

            <StackPanel Background="DarkBlue" Margin="20" Grid.RowSpan="2" HorizontalAlignment="Center" Grid.Row="1"
                        Orientation="Vertical">
                <Grid HorizontalAlignment="Center" Name="numbperpad">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition />
                        <ColumnDefinition />
                        <ColumnDefinition />
                        <ColumnDefinition />
                    </Grid.ColumnDefinitions>

                    <Grid.RowDefinitions>
                        <RowDefinition />
                        <RowDefinition />
                        <RowDefinition />
                        <RowDefinition />
                    </Grid.RowDefinitions>

                    <Button Content="1" Grid.Column="0" Grid.Row="0" Height="100" Width="100" Background="Blue"
                            Foreground="White" FontSize="55" Name="btn1" Click="btn1_Click" />
                    <Button Content="2" Grid.Column="1" Grid.Row="0" Height="100" Width="100" Background="Blue"
                            Foreground="White" FontSize="55" Name="btn2" Click="btn2_Click" />
                    <Button Content="3" Grid.Column="2" Grid.Row="0" Height="100" Width="100" Background="Blue"
                            Foreground="White" FontSize="55" Name="btn3" Click="btn3_Click" />

                    <Button Content="4" Grid.Column="0" Grid.Row="1" Height="100" Width="100" Background="Blue"
                            Foreground="White" FontSize="55" Name="btn4" Click="btn4_Click" />
                    <Button Content="5" Grid.Column="1" Grid.Row="1" Height="100" Width="100" Background="Blue"
                            Foreground="White" FontSize="55" Name="btn5" Click="btn5_Click" />
                    <Button Content="6" Grid.Column="2" Grid.Row="1" Height="100" Width="100" Background="Blue"
                            Foreground="White" FontSize="55" Name="btn6" Click="btn6_Click" />

                    <Button Content="7" Grid.Column="0" Grid.Row="2" Height="100" Width="100" Background="Blue"
                            Foreground="White" FontSize="55" Name="btn7" Click="btn7_Click" />
                    <Button Content="8" Grid.Column="1" Grid.Row="2" Height="100" Width="100" Background="Blue"
                            Foreground="White" FontSize="55" Name="btn8" Click="btn8_Click" />
                    <Button Content="9" Grid.Column="2" Grid.Row="2" Height="100" Width="100" Background="Blue"
                            Foreground="White" FontSize="55" Name="btn9" Click="btn9_Click" />

                    <Button Content="Clear" Grid.Column="0" Grid.Row="3" Height="100" Width="100" FontSize="20"
                            Name="btnclear" Click="btnclear_Click">
                        <Button.Background>
                            <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                                <GradientStop Color="#FF373820" Offset="0.847" />
                                <GradientStop Color="#FFD4DC2E" Offset="0" />
                            </LinearGradientBrush>
                        </Button.Background>
                    </Button>
                    <Button Content="0" Background="Blue" Foreground="White" Grid.Column="1" Grid.Row="3" Height="100"
                            Width="100" FontSize="55" Name="btn0" Click="btn0_Click" />
                    <Button Content="Backspace" Grid.Column="2" Grid.Row="3" Height="100" Width="100" FontSize="20"
                            Name="btnBackSpace" Click="btnBackSpace_Click">
                        <Button.Background>
                            <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                                <GradientStop Color="#FF0D656E" Offset="0.847" />
                                <GradientStop Color="#FF0C8198" Offset="1" />
                            </LinearGradientBrush>
                        </Button.Background>
                        <Button.BorderBrush>
                            <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                                <GradientStop Color="Black" Offset="0" />
                                <GradientStop Color="#FF0C5A66" Offset="0" />
                            </LinearGradientBrush>
                        </Button.BorderBrush>
                    </Button>
                </Grid>
            </StackPanel>
            <Border Margin="20 20 0 0" HorizontalAlignment="Left" Grid.Row="2" BorderBrush="#5387FC"
                    BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10">
                <Button Name="BtnLogin" BorderThickness="0" Background="Transparent"
                        Click="SaveDenomination_Click"
                        Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                        Height="72" VerticalAlignment="Top">
                    Login
                </Button>
            </Border>
        </Grid>
    </Grid>
</Window>