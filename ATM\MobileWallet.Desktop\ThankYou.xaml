﻿<Window x:Class="MobileWallet.Desktop.ThankYou"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        WindowStyle="None"
        Loaded="ThankYou_OnLoaded"
        Title="ThankYou" Height="900" Width="1600" WindowState="Maximized">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="750"/>
            <RowDefinition Height="150"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Image Grid.Column="0" Grid.Row="0" Grid.RowSpan="3" Stretch="Fill" Source="./images/back-drop.png "/>
        <Border Background="#2F4C8E" HorizontalAlignment="Center" VerticalAlignment="Center" Padding="60 40 60 40">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Name="Thanks" HorizontalAlignment="Center" FontWeight="Bold" Foreground="#ffffff" FontSize="40" Width="auto" TextWrapping="Wrap" VerticalAlignment="Center" Text="Thank You . . ." />
            </StackPanel>
        </Border>
        <!--<TextBlock Name="TransactionProcessing" Text="Thank You" Grid.Row="1" FontSize="40" Foreground="White" HorizontalAlignment="Center" FontWeight="Bold" Width="auto"></TextBlock>-->
    </Grid>
</Window>
