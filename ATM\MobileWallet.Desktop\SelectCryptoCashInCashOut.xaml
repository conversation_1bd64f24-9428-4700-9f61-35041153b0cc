﻿<Window x:Class="MobileWallet.Desktop.SelectCryptoCashInCashOut"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:MobileWallet.Desktop"
        WindowStyle="None"
        Closing="SelectCryptoCashInCashOut_OnClosing"
        Loaded="SelectCryptoCashInCashOut_OnLoaded"
        mc:Ignorable="d"
        Title="SelectCryptoCashInCashOut" Height="1600" Width="900" WindowState="Maximized">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="150"/>
            <RowDefinition Height="150"/>
            <RowDefinition Name="DynamicRow" Height="500"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <Image Grid.Column="0" Grid.Row="0" Grid.RowSpan="4" Stretch="Fill" Source="./images/back-drop.png "/>
        <Image Grid.Row="0" Width="250" Source="./images/logo.png" />

        <TextBlock Name="MyHeader" HorizontalAlignment="Center" Grid.Row="1" Margin="0 20 0 0" FontWeight="Bold" Foreground="#ffffff" FontSize="50" TextWrapping="Wrap" Text="Select Crytpo Cash In / Out" />

        <StackPanel HorizontalAlignment="Right" Grid.Row="2" VerticalAlignment="Center">
            <Border Name="BtnDepositBorder" Margin="0 0 20 0" HorizontalAlignment="Right" Grid.Row="0" Grid.RowSpan="3" BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
                <Button Margin="0 0 0 0" Name="BtnBuy" BorderThickness="0" Background="Transparent" Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                    Click="Button_Click_Buy"  >Buy</Button>
            </Border>
            <Border Name="BtnWithdrawBorder" Margin="0 50 20 0" HorizontalAlignment="Right"  Grid.Row="0" Grid.RowSpan="3" BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
                <Button Margin="0 0 0 0" Name="BtnWithdraw" BorderThickness="0" Background="Transparent"  Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                    Click="Button_Click_Withdrawl"  >Sell</Button>
            </Border>
            <Border Name="BtnWithdrawResume" Margin="0 50 20 0" HorizontalAlignment="Right"  Grid.Row="0" Grid.RowSpan="3" BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
                <Button Margin="0 0 0 0" Name="BtnResume" BorderThickness="0" Background="Transparent"  Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                    Click="Button_Click_Resume"  >Resume</Button>
            </Border>
        </StackPanel>
        <StackPanel VerticalAlignment="Center" HorizontalAlignment="Left" Grid.Row="2">
            <Border Margin="20 0 0 0" HorizontalAlignment="Left" Grid.Row="0" Grid.RowSpan="3" BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
                <Button Margin="0 0 0 0" Name="Back" BorderThickness="0" Background="Transparent" Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
            Click="Button_Click_Back"  >Back</Button>
            </Border>
            <Border Margin="20 50 0 0" HorizontalAlignment="Left"  Grid.Row="0" Grid.RowSpan="3" BorderBrush="#FC5353" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
                <Button Margin="0 0 0 0" Name="Cancel" BorderThickness="0" Background="Transparent" FontWeight="DemiBold" Foreground="#FC5353" FontSize="35"
            Click="Button_Click_Cancel"  >Cancel</Button>
            </Border>
        </StackPanel>
    </Grid>
</Window>
