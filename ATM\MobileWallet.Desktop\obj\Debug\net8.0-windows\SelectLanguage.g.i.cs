﻿#pragma checksum "..\..\..\SelectLanguage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "178E38D4C47DF3412275B778746B7189150C0C87"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MobileWallet.Desktop {
    
    
    /// <summary>
    /// SelectLanguage
    /// </summary>
    public partial class SelectLanguage : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 15 "..\..\..\SelectLanguage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RowDefinition DynamicRow;
        
        #line default
        #line hidden
        
        
        #line 20 "..\..\..\SelectLanguage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectLaunguage;
        
        #line default
        #line hidden
        
        
        #line 27 "..\..\..\SelectLanguage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button English;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\SelectLanguage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button french;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\SelectLanguage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnnext;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\SelectLanguage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Back;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\SelectLanguage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Cancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MobileWallet.Desktop;V1.0.0.0;component/selectlanguage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\SelectLanguage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 8 "..\..\..\SelectLanguage.xaml"
            ((MobileWallet.Desktop.SelectLanguage)(target)).Closing += new System.ComponentModel.CancelEventHandler(this.SelectLanguage_OnClosing);
            
            #line default
            #line hidden
            
            #line 9 "..\..\..\SelectLanguage.xaml"
            ((MobileWallet.Desktop.SelectLanguage)(target)).Loaded += new System.Windows.RoutedEventHandler(this.SelectLanguage_OnLoaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.DynamicRow = ((System.Windows.Controls.RowDefinition)(target));
            return;
            case 3:
            this.SelectLaunguage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.English = ((System.Windows.Controls.Button)(target));
            
            #line 26 "..\..\..\SelectLanguage.xaml"
            this.English.GotFocus += new System.Windows.RoutedEventHandler(this.OnGotFocusHandler);
            
            #line default
            #line hidden
            
            #line 27 "..\..\..\SelectLanguage.xaml"
            this.English.LostFocus += new System.Windows.RoutedEventHandler(this.OnLostFocusHandler);
            
            #line default
            #line hidden
            
            #line 29 "..\..\..\SelectLanguage.xaml"
            this.English.Click += new System.Windows.RoutedEventHandler(this.Button_Click_2);
            
            #line default
            #line hidden
            return;
            case 5:
            this.french = ((System.Windows.Controls.Button)(target));
            
            #line 35 "..\..\..\SelectLanguage.xaml"
            this.french.GotFocus += new System.Windows.RoutedEventHandler(this.OnGotFocusHandler);
            
            #line default
            #line hidden
            
            #line 36 "..\..\..\SelectLanguage.xaml"
            this.french.LostFocus += new System.Windows.RoutedEventHandler(this.OnLostFocusHandler);
            
            #line default
            #line hidden
            
            #line 38 "..\..\..\SelectLanguage.xaml"
            this.french.Click += new System.Windows.RoutedEventHandler(this.Button_Click_3);
            
            #line default
            #line hidden
            return;
            case 6:
            this.btnnext = ((System.Windows.Controls.Button)(target));
            
            #line 49 "..\..\..\SelectLanguage.xaml"
            this.btnnext.Click += new System.Windows.RoutedEventHandler(this.btnnext_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.Back = ((System.Windows.Controls.Button)(target));
            
            #line 58 "..\..\..\SelectLanguage.xaml"
            this.Back.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Back);
            
            #line default
            #line hidden
            return;
            case 8:
            this.Cancel = ((System.Windows.Controls.Button)(target));
            
            #line 66 "..\..\..\SelectLanguage.xaml"
            this.Cancel.Click += new System.Windows.RoutedEventHandler(this.Button_Click_1_Cancel);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

