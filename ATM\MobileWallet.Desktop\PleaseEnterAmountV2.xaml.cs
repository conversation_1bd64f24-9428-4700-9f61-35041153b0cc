﻿using System.ComponentModel;
using System.Text;
using System.Windows;
using ITL;
using ITL.Events;
using MobileWallet.Desktop.API;
using MobileWallet.Desktop.Atm;
using MobileWallet.Desktop.Client;
using MobileWallet.Desktop.Helpers;

namespace MobileWallet.Desktop;

public partial class PleaseEnterAmountV2 : CashDeviceWindow
{
    private CancellationTokenSource cancellationTokenSource;
    private CreateWithdrawRequestResponseModel result;

    public PleaseEnterAmountV2()
    {
        InitializeComponent();
        Set_Language();
        cancellationTokenSource = new CancellationTokenSource();
    }

    private void Button_Click(object sender, RoutedEventArgs e) { }

    private void Button_Click_1(object sender, RoutedEventArgs e)
    {
        PlaceYourCardAlignWithTheCamera NewWindow = new PlaceYourCardAlignWithTheCamera();
        NewWindow.Show();
        this.Close();
    }

    private void Button_Click_2(object sender, RoutedEventArgs e)
    {
        Window10 NewWindow = new Window10();
        NewWindow.Show();
        this.Close();
    }

    private void xaf100_Click(object sender, RoutedEventArgs e)
    {
        txtAmount.Text = "100";
    }

    private void xaf500_Click(object sender, RoutedEventArgs e)
    {
        txtAmount.Text = "500";
    }

    private void xaf2000_Click(object sender, RoutedEventArgs e)
    {
        txtAmount.Text = "5000";
    }

    private void xaf5000_Click(object sender, RoutedEventArgs e)
    {
        txtAmount.Text = "5";
    }

    private void SetAccountNumber(string value)
    {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.Append(value);
        string finalvalue = stringBuilder.ToString();
        txtAmount.Text += finalvalue;
    }

    private void btn1_Click(object sender, RoutedEventArgs e)
    {
        SetAccountNumber(btn1.Content.ToString());
    }

    private void btn2_Click(object sender, RoutedEventArgs e)
    {
        SetAccountNumber(btn2.Content.ToString());
    }

    private void btn3_Click(object sender, RoutedEventArgs e)
    {
        SetAccountNumber(btn3.Content.ToString());
    }

    private void btn4_Click(object sender, RoutedEventArgs e)
    {
        SetAccountNumber(btn4.Content.ToString());
    }

    private void btn5_Click(object sender, RoutedEventArgs e)
    {
        SetAccountNumber(btn5.Content.ToString());
    }

    private void btn6_Click(object sender, RoutedEventArgs e)
    {
        SetAccountNumber(btn6.Content.ToString());
    }

    private void btn7_Click(object sender, RoutedEventArgs e)
    {
        SetAccountNumber(btn7.Content.ToString());
    }

    private void btn8_Click(object sender, RoutedEventArgs e)
    {
        SetAccountNumber(btn8.Content.ToString());
    }

    private void btn9_Click(object sender, RoutedEventArgs e)
    {
        SetAccountNumber(btn9.Content.ToString());
    }

    private void btn0_Click_1(object sender, RoutedEventArgs e)
    {
        SetAccountNumber(btn0.Content.ToString());
    }

    //private void btn0_Click(object sender, RoutedEventArgs e)
    //{
    //    SetAccountNumber(btn0.Content.ToString());
    //}

    private void btnremovenumber_Click(object sender, RoutedEventArgs e) { }

    private void btnclear_Click(object sender, RoutedEventArgs e)
    {
        txtAmount.Text = string.Empty;
    }

    private void btndone_Click(object sender, RoutedEventArgs e)
    {
        if (!string.IsNullOrWhiteSpace(txtAmount.Text))
        {
            int length = txtAmount.Text.Length;
            string restnumbers = txtAmount.Text.Substring(0, length - 1);
            txtAmount.Text = restnumbers;
        }
    }

    private void btnclear_Click_1(object sender, RoutedEventArgs e)
    {
        txtAmount.Text = "";
    }

    private void btndone_Click_1(object sender, RoutedEventArgs e)
    {
        try
        {
            if (!string.IsNullOrWhiteSpace(txtAmount.Text))
            {
                int length = txtAmount.Text.Length;
                string restnumbers = txtAmount.Text.Substring(0, length - 1);
                txtAmount.Text = restnumbers;
            }
        }
        catch (Exception ex)
        {
            App.AppLogger.Error(ex, ex.Message);
        }
    }

    private async Task HandleCryptoFlowDemo(int amount, string accountNumber)
    {
        var bitCoinClient = new CryptoClient(HttpClientSingleton.Instance);
        var isBuy = Global.IsDeposit;
        var data = await bitCoinClient.Crypto_GetCryptoQuoteAsync(
            isBuy,
            amount,
            Global.SelectedToken.Address,
            Global.Currency
        );
        var totalAmountToSend = data.Quote;
        if (totalAmountToSend < 0)
        {
            CustomMessageBox.Show("Cannot process Low Amount");
            return;
        }
        var dialogResult = new SelectCryptoRateInfoDialog(data, amount, true).ShowDialog();
        // bool? dialogResult = CustomMessageBox.ShowDialog(
        //     Global.IsFrench
        //         ? (
        //             $"Montant actuel du retrait : {amount}\n"
        //             + $"{data.Quote} {Global.SelectedToken.Symbol} = {amount} XAF\n"
        //             + $"{(amount / data.Quote):N} {Global.SelectedToken.Symbol} = {amount} XAF\n"
        //             + $"Frais de gaz : {data.GasFee}\n"
        //             + $"Vous allez envoyer : {totalAmountToSend} {Global.SelectedToken.Symbol}\n"
        //             + "Voulez-vous continuer ?"
        //         )
        //         : (
        //             $"Current Withdraw Amount: {amount}\n"
        //             + $"1 {Global.SelectedToken.Symbol} = {data.Quote} XAF\n"
        //             + $"{(amount / data.Quote):N} {Global.SelectedToken.Symbol} = {amount} XAF\n"
        //             + $"Gas Fee: {data.GasFee}\n"
        //             + $"You will send:{totalAmountToSend} {Global.SelectedToken.Symbol}\n"
        //             + $"Do you want to proceed?"
        //         ),
        //     "",
        //     MessageBoxButton.YesNo
        // );
        if (dialogResult == true)
        {
            var result = await bitCoinClient.Crypto_CreateCryptoQuoteAsync(
                new API.CreateCryptoQuoteRequestModel()
                {
                    SessionId = TokenManager.SessionId,
                    Address = Global.SelectedToken.Address,
                    AmountIn = data.Quote,
                    AmountOut = amount,
                    AvgPrice = data.AvgPrice,
                    IsBuy = false,
                    UserAddress = Global.UserAddress,
                    PhoneNumber = Global.CurrentAccountNumber,
                    TotalLocal = data.TotalLocal,
                    TotalUsd = data.TotalUsd,
                    Currency = Global.Currency,
                }
            );
            if (result != null)
            {
                WithdrawWaitV2 withdrawWait = new WithdrawWaitV2(
                    result.Data,
                    accountNumber,
                    amount,
                    data
                );
                withdrawWait.Show();
                Close();
                App.HideProcessingDialog();
            }
        }
        else
        {
            App.HideProcessingDialog();
        }
    }

    private async void Button_Click_Submit(object sender, RoutedEventArgs e)
    {
        try
        {
            ButtonHelper.ToggleButton(sender);
            var isValid = ReceiptPrinter.IsValid();
            if (isValid != 0)
            {
                CustomMessageBox.Show(
                    "Receipt is not available at the moment. Please try again later"
                );
                return;
            }

            string accountNumber = Global.CurrentAccountNumber;
            var amount = int.Parse(txtAmount.Text);
            if (amount < 5)
            {
                CustomMessageBox.Show("Cannot Withdraw less than $5");
                return;
            }

            if (amount > 5000)
            {
                CustomMessageBox.Show("Cannot Withdraw more than $5000");
                return;
            }

            if (amount % 1 != 0)
            {
                CustomMessageBox.Show("Amount should be divisible by 1");
                return;
            }

            if (Global.UseHardware)
            {
                if (!await WithdrawHelper.IsAvailableCurrencyV2(amount))
                {
                    CustomMessageBox.Show("Notes Not Available for USD " + amount);
                    return;
                }
            }
            App.ShowProcessingDialog();
            if (Global.IsCrypto)
            {
                await HandleCryptoFlowDemo(amount, accountNumber);
            }
            else
            {
                await HandleMtnFlow(amount, accountNumber);
            }
        }
        catch (Exception exception)
        {
            App.AppLogger.Error(exception, exception.Message);
            App.HideProcessingDialog();
            CustomMessageBox.Show("Unable to Process Withdraw");
        }
        finally
        {
            ButtonHelper.ToggleButton(sender);
        }
    }

    private async Task HandleMtnFlow(int amount, string accountNumber)
    {
        var transactionClient = new MtnClient(HttpClientSingleton.Instance);
        result = await transactionClient.Mtn_CreateWithdrawRequestAsync(
            new CreateWithdrawRequestRequestModel()
            {
                Amount = int.Parse(txtAmount.Text),
                PhoneNumber = Global.CurrentAccountNumber,
                SessionId = TokenManager.SessionId,
                Currency = Global.Currency,
            }
        );
        while (true)
        {
            await Task.Delay(5000);
            var status = "PENDING";
            var reason = "";
            try
            {
                var response = (
                    await transactionClient.Mtn_GetTransactionInfoAsync(result.Data.TransactionId)
                ).Data;
                status = response?.Status;
                reason = response?.Reason;
            }
            catch (Exception exception)
            {
                App.AppLogger.Error(exception, exception.Message);
                Console.WriteLine(exception);
            }

            if (status == "PENDING")
            {
                continue;
            }

            if (status == "FAILED")
            {
                App.HideProcessingDialog();
                CustomMessageBox.Show("Transaction Failed: " + new Helper().GetReason(reason));
                WelcomeToMobileWallet welcomeToMobileWallet = new WelcomeToMobileWallet();
                welcomeToMobileWallet.Show();
                Close();
                return;
            }

            break;
        }

        if (Global.UseHardware)
        {
            _ = App.TrackAtmRealTime(new UpdateAtmRealTimeRequestModel() { IsWithdraw = true });
            var resultDispenseValue = App.CashDevice.DispenseValue(
                new ValueCountryCode((uint)amount * 100, "USD")
            );
            if (resultDispenseValue == DispenseTransactionRequestResult.OK) { }
            else
            {
                switch (resultDispenseValue)
                {
                    case DispenseTransactionRequestResult.NOT_SUPPORTED:
                        CustomMessageBox.ShowDialog("This function is not supported.");
                        break;
                    case DispenseTransactionRequestResult.BUSY:
                        CustomMessageBox.ShowDialog(
                            "The dispenser is currently busy. Please try again later."
                        );
                        break;
                    case DispenseTransactionRequestResult.INVALID_INPUT:
                        CustomMessageBox.ShowDialog(
                            "Invalid input provided. Please check the transaction details."
                        );
                        break;
                    case DispenseTransactionRequestResult.NOT_ENOUGH_VALUE:
                        CustomMessageBox.ShowDialog(
                            "Insufficient funds or value to complete the transaction."
                        );
                        break;
                    case DispenseTransactionRequestResult.CANNOT_PAY_EXACT_AMOUNT:
                        CustomMessageBox.ShowDialog(
                            "Unable to dispense the exact amount requested."
                        );
                        break;
                    case DispenseTransactionRequestResult.MULTI_CURRENCY_MODE:
                        CustomMessageBox.ShowDialog(
                            "Multi-currency mode is enabled. Dispensing is not available in this mode."
                        );
                        break;
                }
                var transferResult = await transactionClient.Mtn_CreateTransferRequestAsync(
                    new CreateTransferRequestRequestModel()
                    {
                        Amount = int.Parse(txtAmount.Text),
                        PhoneNumber = Global.CurrentAccountNumber,
                        SessionId = TokenManager.SessionId,
                        Currency = Global.Currency,
                    }
                );
                while (true)
                {
                    await Task.Delay(5000);
                    var status = "PENDING";
                    var reason = "";
                    try
                    {
                        var response = (
                            await transactionClient.Mtn_GetTransactionInfoAsync(
                                result.Data.TransactionId
                            )
                        ).Data;
                        status = response?.Status;
                        reason = response?.Reason;
                    }
                    catch (Exception exception)
                    {
                        App.AppLogger.Error(exception, exception.Message);
                        Console.WriteLine(exception);
                    }

                    if (status == "PENDING")
                    {
                        continue;
                    }

                    if (status != "SUCCESS")
                    {
                        App.HideProcessingDialog();
                        CustomMessageBox.Show("Transaction Failed: " + reason);
                        WelcomeToMobileWallet welcomeToMobileWallet = new WelcomeToMobileWallet();
                        welcomeToMobileWallet.Show();
                        Close();
                        return;
                    }
                    break;
                }
                App.HideProcessingDialog();
                CustomMessageBox.ShowDialog("Please try again. !!");
                WelcomeToMobileWallet newWelcomeToMobileWallet = new WelcomeToMobileWallet();
                newWelcomeToMobileWallet.Show();
                Close();
            }
            return;
        }
        else
        {
            App.HideProcessingDialog();
            ThankYou thankYou = new ThankYou();
            thankYou.Show();
            this.Close();
        }
    }

    private void Button_Click_Cancel(object sender, RoutedEventArgs e)
    {
        App.CashAcceptor.return_escrow_stack();
        WelcomeToMobileWallet NewWindow = new WelcomeToMobileWallet();
        NewWindow.Show();
        Close();
    }

    private void Set_Language()
    {
        try
        {
            switch (Global.DefaultLanguage)
            {
                case "English":
                    EnterAmount.Text = ResourceEnglish.EnterAmountUsd;
                    Submit.Content = ResourceEnglish.Submit;
                    Cancel.Content = ResourceEnglish.Cancel;
                    btndone.Content = ResourceEnglish.btndone;
                    btnclear.Content = ResourceEnglish.btnclear;
                    break;
                case "French":
                    EnterAmount.Text = ResourceFrench.EnterAmountUsd;
                    Submit.Content = ResourceFrench.Submit;
                    Cancel.Content = ResourceFrench.Cancel;
                    btndone.Content = ResourceFrench.btndone;
                    btnclear.Content = ResourceFrench.btnclear;
                    break;
            }
        }
        catch (Exception ex)
        {
            App.AppLogger.Error(ex, ex.Message);
        }
    }

    private void xaf10000_Click(object sender, RoutedEventArgs e)
    {
        txtAmount.Text = "10";
    }

    private void xaf25000_Click(object sender, RoutedEventArgs e)
    {
        txtAmount.Text = "20";
    }

    private void xaf50000_Click(object sender, RoutedEventArgs e)
    {
        txtAmount.Text = "50000";
    }

    private void xaf100000_Click(object sender, RoutedEventArgs e)
    {
        txtAmount.Text = "50";
    }

    private void xaf150000_Click(object sender, RoutedEventArgs e)
    {
        txtAmount.Text = "100";
    }

    private void xaf200000_Click(object sender, RoutedEventArgs e)
    {
        txtAmount.Text = "500";
    }

    protected override void DispenserTransactionEventHandler(
        object sender,
        DispenserTransactionArgs e
    )
    {
        base.DispenserTransactionEventHandler(sender, e);
        if (e.State == DispenserTransactionState.COMPLETED)
        {
            Dispatcher.Invoke(() =>
            {
                string branchCode = "ABC123";
                string name = Global.Username;
                var amount = int.Parse(txtAmount.Text);
                double cashIn = amount;
                string transactionId = result.Data.TransactionId;
                string date = DateTime.Now.ToString("yyyy-MM-dd");
                string time = DateTime.Now.ToString("hh:mm tt");
                string currency = Global.Currency;
                string accountNo = Global.CurrentAccountNumber;
                //Call the function of recieptPrinter
                var isValid = ReceiptPrinter.IsValid();
                _ = App.TrackAtmRealTime(
                    new UpdateAtmRealTimeRequestModel() { PrintStatus = isValid.ToString() }
                );
                if (Global.UseHardware && isValid == 0)
                {
                    bool isSuccess = ReceiptPrinter.PrintWithdrawReceipt(
                        TokenManager.UserName,
                        name,
                        cashIn,
                        transactionId,
                        date,
                        time,
                        currency,
                        accountNo,
                        new List<ReceiptItem>()
                    );
                    _ = App.LogError("Withdraw Print Completed", LogType.Withdraw, "");
                }
                else if (Global.UseHardware)
                {
                    _ = App.LogError("Printer Error", LogType.Error, "Error Code: " + isValid);
                    CustomMessageBox.Show("Receipt is not available at the moment");
                }

                if (true)
                {
                    App.HideProcessingDialog();
                    // CustomMessageBox.ShowDialog(
                    //     Global.IsFrench
                    //         ? "Veuillez recuperer vos billets !!"
                    //         : "Please collect your money!!"
                    // );
                    ThankYou thankYou = new ThankYou();
                    thankYou.Show();
                    this.Close();
                }
            });
        }

        if (e.State == DispenserTransactionState.ERROR)
        {
            DispenserTransactionErrorReason resultDispenseValue = e.Data.ErrorReason;
            Dispatcher.Invoke(async () =>
            {
                App.HideProcessingDialog();
                switch (e.Data.ErrorReason)
                {
                    case DispenserTransactionErrorReason.BUSY:
                        CustomMessageBox.Show(
                            "Transaction was not completed due to the system being busy"
                        );
                        break;
                    case DispenserTransactionErrorReason.NOT_ENOUGH_VALUE:
                    case DispenserTransactionErrorReason.CANNOT_PAY_EXACT_AMOUNT:
                        CustomMessageBox.Show(
                            "Transaction was not completed due to not having enough money"
                        );
                        break;
                    case DispenserTransactionErrorReason.DEVICE_DISABLED:
                        CustomMessageBox.Show(
                            "Transaction was not completed due to device being disabled"
                        );
                        break;
                    case DispenserTransactionErrorReason.ERROR_DURING_PAYOUT:
                        CustomMessageBox.Show("Transaction was not completed due to payout error");
                        break;
                    case DispenserTransactionErrorReason.JAMMED:
                        CustomMessageBox.Show("Transaction was not completed due to Jam");
                        break;
                    case DispenserTransactionErrorReason.HALTED:
                        CustomMessageBox.Show("Transaction was not completed due to Halted");
                        break;
                    case DispenserTransactionErrorReason.UNKNOWN:
                        break;
                    case DispenserTransactionErrorReason.TIME_OUT:
                        CustomMessageBox.Show("Transaction was not completed due to timeout error");
                        break;
                }

                var transactionClient = new MtnClient(HttpClientSingleton.Instance);
                var transferResult = await transactionClient.Mtn_CreateTransferRequestAsync(
                    new CreateTransferRequestRequestModel()
                    {
                        Amount = int.Parse(txtAmount.Text),
                        PhoneNumber = Global.CurrentAccountNumber,
                        SessionId = TokenManager.SessionId,
                        Currency = Global.Currency,
                    }
                );
                while (true)
                {
                    await Task.Delay(5000);
                    var status = "PENDING";
                    var reason = "";
                    try
                    {
                        var response = (
                            await transactionClient.Mtn_GetTransactionInfoAsync(
                                result.Data.TransactionId
                            )
                        ).Data;
                        status = response?.Status;
                        reason = response?.Reason;
                    }
                    catch (Exception exception)
                    {
                        App.AppLogger.Error(exception, exception.Message);
                        Console.WriteLine(exception);
                    }

                    if (status == "PENDING")
                    {
                        continue;
                    }

                    if (status != "SUCCESS")
                    {
                        App.HideProcessingDialog();
                        CustomMessageBox.Show("Transaction Failed: " + reason);
                        WelcomeToMobileWallet welcomeToMobileWallet = new WelcomeToMobileWallet();
                        welcomeToMobileWallet.Show();
                        Close();
                        return;
                    }
                    break;
                }
                App.HideProcessingDialog();
                CustomMessageBox.ShowDialog("Please try again. !!");
                WelcomeToMobileWallet newWelcomeToMobileWallet = new WelcomeToMobileWallet();
                newWelcomeToMobileWallet.Show();
                Close();
            });
        }
    }

    private async void OnWindowLoad(object sender, RoutedEventArgs e)
    {
        Dispatcher.Invoke(() =>
        {
            _ = App.TrackAtmRealTime(
                new UpdateAtmRealTimeRequestModel() { CurrentScreen = nameof(PleaseEnterAmountV2) }
            );
            InitCashDevice();
        });
    }

    private void OnWindowClose(object? sender, CancelEventArgs e)
    {
        Dispatcher.Invoke(() =>
        {
            UnInitCashDevice();
        });
    }
}
