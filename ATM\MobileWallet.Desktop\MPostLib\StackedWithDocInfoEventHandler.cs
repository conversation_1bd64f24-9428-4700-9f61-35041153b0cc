﻿// Decompiled with JetBrains decompiler
// Type: MPOST.StackedWithDocInfoEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("08ACB881-F269-4B53-8E85-6F3C7DF1AB0A")]
  [ComVisible(true)]
  public delegate void StackedWithDocInfoEventHandler(object sender, StackedEventArgs e);
}
