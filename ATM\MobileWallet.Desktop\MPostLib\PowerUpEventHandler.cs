﻿// Decompiled with JetBrains decompiler
// Type: MPOST.PowerUpEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("3EABE85B-3F87-4012-86D0-9807D692A451")]
  [ComVisible(true)]
  public delegate void PowerUpEventHandler(object sender, EventArgs e);
}
