﻿import time

import serial
from serial.serialwin32 import Serial
def main(port_name, command_str):
    try:
        # Open the serial port with the specified parameters
        ser = Serial()
        ser.port = port_name
        ser.baudrate = 9600
        ser.bytesize = serial.SEVENBITS
        ser.parity = serial.PARITY_EVEN
        ser.stopbits = serial.STOPBITS_ONE
        ser.timeout = 0  # Set a timeout for reading (in seconds)
        ser.open()

        if ser.is_open:
            print(f"Port {port_name} opened successfully.")

            # Convert command_str to bytes and write it to the serial port
            command_bytes = command_str.encode('utf-8')
            ser.write(command_bytes)
            print("Command sent successfully.")
            # Initialize a buffer to store the response data
            response = ""
            retry = 0
            # Continuously read from the serial port
            while True:
                retry += 1
                time.sleep(1)
                if retry == 180:
                    print("breaking after Retry: " + str(retry))
                    break
                if ser.in_waiting > 0:
                    print("Found Read from Serial: " + str(ser.in_waiting))
                    chunk = ser.read(size=ser.in_waiting)  # Read all available bytes in the buffer
                    if chunk:
                        response += chunk.decode('utf-8', errors='ignore')
                        print("Read from Serial: " + str(response))
                        break
            if response:
                print("Response received:", response)
                return response
            else:
                print("No response received.")
                return ""

        else:
            print(f"Failed to open port {port_name}.")
            return None

    except Exception as e:
        print(f"An error occurred: {e}")
        return None

    finally:
        # Close the serial port
        if 'ser' in locals() and ser.is_open:
            ser.close()
            print(f"Port {port_name} closed.")
