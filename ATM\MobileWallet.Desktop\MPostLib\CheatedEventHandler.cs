﻿// Decompiled with JetBrains decompiler
// Type: MPOST.CheatedEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("DB383888-4E0E-4b9d-9C8F-EBD5492C149E")]
  [ComVisible(true)]
  public delegate void CheatedEventHandler(object sender, EventArgs e);
}
