﻿// Decompiled with JetBrains decompiler
// Type: MPOST.PupExt
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("220E5480-8E68-4a54-8EE5-8AACDB6643A5")]
  [ComVisible(true)]
  public enum PupExt
  {
    OutOfService,
    Return,
    StackNoCredit,
    Stack,
    WaitNoCredit,
    Wait,
  }
}
