﻿<Window x:Class="MobileWallet.Desktop.CaptureIdCard"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        mc:Ignorable="d"
        WindowStyle="None"
        Loaded="CaptureIdCard_OnLoaded"
        Closing="CaptureIdCard_OnClosing"
        Title="CaptureIdCard" Width="900" Height="1600" WindowState="Maximized">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="150" />
            <RowDefinition Height="150" />
            <RowDefinition Name="DynamicRow" Height="500" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>


        <Image Grid.Column="0" Grid.Row="0" Grid.RowSpan="4" Grid.ColumnSpan="3" Stretch="Fill"
               Source="./images/back-drop.png " />
        <Image Grid.Row="0" Grid.ColumnSpan="3" Width="250" Source="./images/logo.png" />

        <TextBlock Name="WindowTitle" HorizontalAlignment="Center" Grid.ColumnSpan="3" Grid.Row="1" Margin="0 10 0 0"
                   FontWeight="Bold" Foreground="#ffffff" FontSize="50" TextWrapping="Wrap"
                   Text="Place your Id card towards the camera" />


        <StackPanel HorizontalAlignment="Left" VerticalAlignment="Center" Grid.Row="2" Grid.Column="0">
            <Border Margin="20 0 0 0" HorizontalAlignment="Left" Grid.Row="0" Grid.RowSpan="3" Grid.ColumnSpan="3"
                    BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80"
                    CornerRadius="10">
                <Button Name="BtnBack" Margin="0 0 0 0" BorderThickness="0" Background="Transparent"
                        FontWeight="DemiBold" Foreground="#5387FC" FontSize="35"
                        Click="Button_Click">
                    Back
                </Button>
            </Border>
            <Border Margin="20 50 0 0" HorizontalAlignment="Left" Grid.Row="2" Grid.RowSpan="3" Grid.ColumnSpan="3"
                    BorderBrush="#FC5353" BorderThickness="4" Width="280" Background="#ffffff" Height="80"
                    CornerRadius="10">
                <Button Name="BtnCancel" Margin="0 0 0 0" BorderThickness="0" Background="Transparent"
                        FontWeight="DemiBold" Foreground="#FC5353" FontSize="35"
                        Click="Button_Click_Cancel">
                    Cancel
                </Button>
            </Border>
        </StackPanel>
        <StackPanel HorizontalAlignment="Right" VerticalAlignment="Center" Grid.Row="2" Grid.Column="2">
            <Border Margin="0 0 20 0" HorizontalAlignment="Right" Grid.Row="0" Grid.RowSpan="3" Grid.ColumnSpan="3"
                    BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80"
                    CornerRadius="10">
                <Button Name="BtnCapture" BorderThickness="0" Background="Transparent" Foreground="#5387FC"
                        FontWeight="DemiBold" FontSize="35"
                        Height="72" VerticalAlignment="Top" Click="Button_Click_Capture">
                    Capture
                </Button>
            </Border>
            <Border Name="SubmitBorder" Margin="0 50 20 0" HorizontalAlignment="Right" Grid.Row="0" Grid.RowSpan="3"
                    Grid.ColumnSpan="3" BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff"
                    Height="80" CornerRadius="10">
                <Button Name="BtnSubmit" BorderThickness="0" Background="Transparent" Foreground="#5387FC"
                        FontWeight="DemiBold" FontSize="35"
                        Height="72" VerticalAlignment="Top" Click="Button_Click_Submit">
                    Submit
                </Button>
            </Border>
        </StackPanel>
        <StackPanel Orientation="Vertical" VerticalAlignment="Top" Grid.Row="2" Grid.Column="1">
            <Image x:Name="CameraImage" Stretch="Fill" />
        </StackPanel>


    </Grid>
</Window>