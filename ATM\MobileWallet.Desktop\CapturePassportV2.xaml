﻿<Window x:Class="MobileWallet.Desktop.CapturePassportV2"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:MobileWallet.Desktop"
        WindowStyle="None"
        mc:Ignorable="d"
        Loaded="CapturePassport_OnLoaded"
        Closing="CapturePassport_OnClosing"
        WindowState="Maximized"
        Title="CapturePassportV2" Height="900" Width="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="150" />
            <RowDefinition Height="150" />
            <RowDefinition Name="DynamicRow" Height="500" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>


        <Image Grid.Column="0" Grid.Row="0" Grid.RowSpan="4" Grid.ColumnSpan="3" Stretch="Fill"
               Source="./images/back-drop.png " />
        <Image Grid.Row="0" Grid.ColumnSpan="3" Width="250" Source="./images/logo.png" />

        <TextBlock Name="WindowTitle" HorizontalAlignment="Center" Grid.ColumnSpan="3" Grid.Row="1" Margin="0 10 0 0"
                   FontWeight="Bold" Foreground="#ffffff" FontSize="50" TextWrapping="Wrap"
                   Text="Place your passport inside the scanner" />


        <StackPanel Grid.Column="0" VerticalAlignment="Center" Grid.Row="2">
            <Border Margin="20 0 20 0" HorizontalAlignment="Left" BorderBrush="#FC5353" BorderThickness="4" Width="280"
                    Background="#ffffff" Height="80" CornerRadius="10">
                <Button Name="BtnBack" Margin="0 0 0 0" BorderThickness="0" Background="Transparent"
                        FontWeight="DemiBold" Foreground="#FC5353" FontSize="35"
                        Click="Button_Click">
                    Back
                </Button>
            </Border>
            <Border Margin="20 50 20 0" HorizontalAlignment="Left" Grid.Row="0" Grid.RowSpan="3" Grid.ColumnSpan="3"
                    BorderBrush="#FC5353" BorderThickness="4" Width="280" Background="#ffffff" Height="80"
                    CornerRadius="10">
                <Button Name="BtnCancel" Margin="0 0 0 0" BorderThickness="0" Background="Transparent"
                        FontWeight="DemiBold" Foreground="#FC5353" FontSize="35"
                        Click="Button_Click_Cancel">
                    Cancel
                </Button>
            </Border>
        </StackPanel>
        <StackPanel Grid.Row="2" Grid.Column="2" VerticalAlignment="Center">
            <Border Name="CaptureBorder" Margin="0 0 20 0" HorizontalAlignment="Right"
                    BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80"
                    CornerRadius="10">
                <Button Name="BtnCapture" BorderThickness="0" Background="Transparent" Foreground="#5387FC"
                        FontWeight="DemiBold" FontSize="35"
                        Height="72" VerticalAlignment="Top" Click="Button_Click_Capture">
                    Capture
                </Button>
            </Border>
            <Border Name="SubmitBorder" Margin="0 50 20 0" HorizontalAlignment="Right" BorderBrush="#5387FC"
                    BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10">
                <Button Name="BtnSubmit" BorderThickness="0" Background="Transparent" Foreground="#5387FC"
                        FontWeight="DemiBold" FontSize="35"
                        Height="72" VerticalAlignment="Top" Click="Button_Click_Submit">
                    Submit
                </Button>
            </Border>
        </StackPanel>
        <StackPanel Orientation="Vertical" Background="Transparent" VerticalAlignment="Top" Grid.Row="2"
                    Grid.RowSpan="2" Grid.ColumnSpan="3" Grid.Column="0">
            <TextBlock TextAlignment="Center" FontWeight="Bold" Foreground="#ffffff" FontSize="50" TextWrapping="Wrap"
                       Name="TxtPassportDetails">

            </TextBlock>
        </StackPanel>

    </Grid>
</Window>