﻿<Window x:Class="MobileWallet.Desktop.WithdrawWait"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        mc:Ignorable="d"
        WindowStyle="None"
        Loaded="WithdrawWait_OnLoaded"
        Height="1600" Width="900" WindowState="Maximized"
        Title="WithdrawWait">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Image Grid.Column="0" Grid.Row="0" Stretch="Fill" Source="./images/back-drop.png "/>
        <Border Grid.Row="0" Background="#2F4C8E" HorizontalAlignment="Center" VerticalAlignment="Center" Padding="40 40 40 40">
            <StackPanel Orientation="Vertical">
                <Image Name="BarcodeImage" Width="300" Margin="0 0 0 20"/>
                <TextBlock Margin="0,10,0,0" Name="TxtAmountToSend" HorizontalAlignment="Center" FontWeight="Bold" Foreground="#ffffff" FontSize="40" Width="auto" TextWrapping="Wrap" VerticalAlignment="Center" Text="SOL 1234.00" />
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <MediaElement x:Name="loaderImage" Source="/images/loader1.gif" Width="80" Margin="0 0 0 0" />
                    <TextBlock Name="TransactionProcessing" HorizontalAlignment="Center" FontWeight="Bold" Foreground="#ffffff" FontSize="40" Width="auto" TextWrapping="Wrap" VerticalAlignment="Center" Text="Processing....." />
                </StackPanel>
            </StackPanel>
        </Border>
    </Grid>
</Window>
