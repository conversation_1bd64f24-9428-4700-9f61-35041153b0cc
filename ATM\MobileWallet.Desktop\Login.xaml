﻿<desktop:CashDeviceWindow x:Class="MobileWallet.Desktop.Login"
                  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                  xmlns:desktop="clr-namespace:MobileWallet.Desktop"
                  mc:Ignorable="d"
                  Loaded="Login_OnLoaded"
                  Closing="Login_OnClosing"
                  WindowStyle="None"
                  Title="Please Set Denominations" Height="1600" Width="900" WindowState="Maximized">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="100" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <Image Grid.Column="0" Grid.Row="0" Grid.RowSpan="2" Stretch="Fill" Source="./images/back-drop.png " />
        <Image Grid.Row="0" Width="250" Source="./images/logo.png" />
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <Label
                    Padding="10"
                    Grid.Row="0"
                    Grid.Column="1"
                    HorizontalAlignment="Center"
                    FontSize="25" FontWeight="Bold" Foreground="Red">
                    Login to Atm
                </Label>
                <Label
                    Padding="10"
                    Grid.Row="1"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Center"
                    FontSize="30" FontWeight="SemiBold" Foreground="Red">
                    Username
                </Label>
                <PasswordBox
                    GotFocus="GotFocus"
                    LostFocus="OnLostFocus"
                    Name="TxtUserName"
                    Grid.Row="1" Grid.Column="1"
                    HorizontalAlignment="Center" Height="60" Width="300" Margin="0,20"   FontSize="40" FontWeight="Bold" />
                <PasswordBox
                    Name="TxtPin"
                    LostFocus="OnLostFocus"
                    GotFocus="GotFocus"
                    Grid.Row="2" Grid.Column="1"
                    HorizontalAlignment="Center" Height="60" Width="300" Margin="0,20"   FontSize="40" FontWeight="Bold" />
                <Label
                    Padding="10"
                    Grid.Column="0"
                    Grid.Row="2"
                    HorizontalAlignment="Center"
                    FontSize="30" FontWeight="SemiBold" Foreground="Red">
                    Pin
                </Label>
            </Grid>
            <Border Margin="20 20 0 0" HorizontalAlignment="Left" Grid.Row="1" VerticalAlignment="Center" BorderBrush="#5387FC"
                    BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10">
                <Button Name="BtnLogin" BorderThickness="0" Background="Transparent"
                        Click="BtnLogin_OnClick"
                        Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                        Height="72" VerticalAlignment="Top">
                    Login
                </Button>
            </Border>
            <Border Margin="20 20 0 0" HorizontalAlignment="Left" Grid.Row="2" VerticalAlignment="Center" BorderBrush="#5387FC"
                    BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10">
                <Button Name="BtnPrintDeposit" BorderThickness="0" Background="Transparent"
                        Click="BtnPrintDeposit_OnClick"
                        Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                        Height="72" VerticalAlignment="Top">
                    Print Deposit
                </Button>
            </Border>
            <Border Margin="20 20 0 0" HorizontalAlignment="Left" Grid.Row="3" VerticalAlignment="Center" BorderBrush="#5387FC"
                    BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10">
                <Button Name="BtnPrintWithdraw" BorderThickness="0" Background="Transparent"
                        Click="BtnPrintWithdraw_OnClick"
                        Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                        Height="72" VerticalAlignment="Top">
                    Print Withdraw
                </Button>
            </Border>
        </Grid>
    </Grid>
</desktop:CashDeviceWindow>