﻿<Window x:Class="MobileWallet.Desktop.SelectCryptoQuotation"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:MobileWallet.Desktop"
        mc:Ignorable="d"
        WindowStyle="None"
        Closing="SelectCryptoQuotation_OnClosing"
        Loaded="SelectCryptoQuotation_OnLoaded"
        Title="SelectCryptoQuotation" Height="1600" Width="900" WindowState="Maximized">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="110" />
            <RowDefinition Height="100" />
            <RowDefinition Height="*" />
            <RowDefinition Height="110" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <Image Grid.Column="0" Grid.Row="0" Grid.ColumnSpan="3" Grid.RowSpan="5" Stretch="Fill"
               Source="./images/back-drop.png " />
        <Image Grid.Row="0" Grid.ColumnSpan="3" Grid.Column="0" Width="250" Source="./images/logo.png" />

        <TextBlock
            TextAlignment="Center"
            Name="MyHeader" HorizontalAlignment="Center" Grid.Row="1"
            Grid.ColumnSpan="3" Grid.Column="0" Margin="20 0 20 0" FontWeight="Bold" Foreground="#ffffff" FontSize="50"
            TextWrapping="Wrap" Text="Verify Crypto Rates" />
        <Border
            VerticalAlignment="Top"
            Grid.Column="2"
            Margin="0 0 20 0" HorizontalAlignment="Right" Grid.Row="3" BorderBrush="#5387FC" BorderThickness="4"
            Width="280" Background="#ffffff" Height="80" CornerRadius="10">
            <Button Margin="0 0 0 0" Name="Next" BorderThickness="0" Background="Transparent" Foreground="#5387FC"
                    FontWeight="DemiBold" FontSize="35"
                    Click="Button_Click_Next">
                Next
            </Button>
        </Border>
        <Border
            VerticalAlignment="Top"
            Grid.Column="2"
            Margin="0 0 20 0" HorizontalAlignment="Right" Grid.Row="4" BorderBrush="#5387FC" BorderThickness="4"
            Width="280" Background="#ffffff" Height="80" CornerRadius="10">
            <Button Margin="0 0 0 0" Name="Submit" BorderThickness="0" Background="Transparent" Foreground="#5387FC"
                    FontWeight="DemiBold" FontSize="35"
                    Click="Button_Click_Submit">
                Submit
            </Button>
        </Border>
        

        <Border
            VerticalAlignment="Top"
            Grid.Column="0"
            Margin="20 0 0 0" HorizontalAlignment="Left" Grid.Row="3" BorderBrush="#5387FC" BorderThickness="4"
            Width="280" Background="#ffffff" Height="80" CornerRadius="10">
            <Button Margin="0 0 0 0" Name="Back" BorderThickness="0" Background="Transparent" Foreground="#5387FC"
                    FontWeight="DemiBold" FontSize="35"
                    Click="Button_Click_Back">
                Back
            </Button>
        </Border>
        <Border
            VerticalAlignment="Top"
            Grid.Column="0"
            Margin="20 0 0 0" HorizontalAlignment="Left" Grid.Row="4" BorderBrush="#FC5353" BorderThickness="4"
            Width="280" Background="#ffffff" Height="80" CornerRadius="10">
            <Button Margin="0 0 0 0" Name="Cancel" BorderThickness="0" Background="Transparent" FontWeight="DemiBold"
                    Foreground="#FC5353" FontSize="35"
                    Click="Button_Click_Cancel">
                Cancel
            </Button>
        </Border>

        <StackPanel
            Grid.RowSpan="3"
            Grid.Column="0"
            Grid.ColumnSpan="3"
            Margin="0,0,0,0" Grid.Row="2" Orientation="Vertical">
            <Label Name="lblMsgOTP" FontSize="35" 
                   Content="Please Enter Amount"
                   HorizontalAlignment="Center"
                   FontWeight="Bold" Foreground="White" Margin="0,20,0,0" />

            <TextBox HorizontalAlignment="Center" MaxLength="6" Height="60" IsReadOnly="True" Width="300" Margin="0,20"
                     FontSize="40" FontWeight="Bold" Name="txtAccountNumber" />

            <Grid HorizontalAlignment="Center" Name="numbperpad">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition />
                    <ColumnDefinition />
                    <ColumnDefinition />
                </Grid.ColumnDefinitions>

                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>

                <Button Content="1" Grid.Column="0" Grid.Row="0" Height="100" Width="100" Background="Blue"
                        Foreground="White" FontSize="55" Name="btn1" Click="btn1_Click_1" />
                <Button Content="2" Grid.Column="1" Grid.Row="0" Height="100" Width="100" Background="Blue"
                        Foreground="White" FontSize="55" Name="btn2" Click="btn2_Click" />
                <Button Content="3" Grid.Column="2" Grid.Row="0" Height="100" Width="100" Background="Blue"
                        Foreground="White" FontSize="55" Name="btn3" Click="btn3_Click" VerticalAlignment="Top" />

                <Button Content="4" Grid.Column="0" Grid.Row="1" Height="100" Width="100" Background="Blue"
                        Foreground="White" FontSize="55" Name="btn4" Click="btn4_Click" />
                <Button Content="5" Grid.Column="1" Grid.Row="1" Height="100" Width="100" Background="Blue"
                        Foreground="White" FontSize="55" Name="btn5" Click="btn5_Click" />
                <Button Content="6" Grid.Column="2" Grid.Row="1" Height="100" Width="100" Background="Blue"
                        Foreground="White" FontSize="55" Name="btn6" Click="btn6_Click" />

                <Button Content="7" Grid.Column="0" Grid.Row="2" Height="100" Width="100" Background="Blue"
                        Foreground="White" FontSize="55" Name="btn7" Click="btn7_Click" />
                <Button Content="8" Grid.Column="1" Grid.Row="2" Height="100" Width="100" Background="Blue"
                        Foreground="White" FontSize="55" Name="btn8" Click="btn8_Click" />
                <Button Content="9" Grid.Column="2" Grid.Row="2" Height="100" Width="100" Background="Blue"
                        Foreground="White" FontSize="55" Name="btn9" Click="btn9_Click" />

                <Button Content="Clear" Grid.Column="0" Grid.Row="3" Height="100" Width="100" FontSize="20"
                        Name="btnclear" Click="btnclear_Click">
                    <Button.Background>
                        <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                            <GradientStop Color="#FF373820" Offset="0.847" />
                            <GradientStop Color="#FFD4DC2E" Offset="0" />
                        </LinearGradientBrush>
                    </Button.Background>
                </Button>
                <Button Content="0" Background="Blue" Foreground="White" Grid.Column="1" Grid.Row="3" Height="100"
                        Width="100" FontSize="55" Name="btn0" Click="btn0_Click" />
                <Button Content="Backspace" 
                        Click="Btndone_OnClick"
                        Grid.Column="2" Grid.Row="3" Height="100" Width="100" FontSize="20"
                        Name="btndone">
                    <Button.Background>
                        <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                            <GradientStop Color="#FF0D656E" />
                            <GradientStop Color="#FF0C8198" Offset="1" />
                        </LinearGradientBrush>
                    </Button.Background>
                </Button>


            </Grid>
        </StackPanel>
    </Grid>
</Window>
