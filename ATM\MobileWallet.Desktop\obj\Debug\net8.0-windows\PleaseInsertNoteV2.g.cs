﻿#pragma checksum "..\..\..\PleaseInsertNoteV2.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "A5E27EF81B424FD5BBDF2B5BD3BE6538812EA114"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MobileWallet.Desktop;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MobileWallet.Desktop {
    
    
    /// <summary>
    /// PleaseInsertNoteV2
    /// </summary>
    public partial class PleaseInsertNoteV2 : MobileWallet.Desktop.CashDeviceWindow, System.Windows.Markup.IComponentConnector {
        
        
        #line 17 "..\..\..\PleaseInsertNoteV2.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RowDefinition DynamicRow;
        
        #line default
        #line hidden
        
        
        #line 26 "..\..\..\PleaseInsertNoteV2.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtInsertNote;
        
        #line default
        #line hidden
        
        
        #line 28 "..\..\..\PleaseInsertNoteV2.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtEscrow;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\PleaseInsertNoteV2.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Submit;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\PleaseInsertNoteV2.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border BorderHelp;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\PleaseInsertNoteV2.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnHelp;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\PleaseInsertNoteV2.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border BackBorder;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\PleaseInsertNoteV2.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Back;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\..\PleaseInsertNoteV2.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border CancelBorder;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\PleaseInsertNoteV2.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Cancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MobileWallet.Desktop;component/pleaseinsertnotev2.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\PleaseInsertNoteV2.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DynamicRow = ((System.Windows.Controls.RowDefinition)(target));
            return;
            case 2:
            this.TxtInsertNote = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TxtEscrow = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.Submit = ((System.Windows.Controls.Button)(target));
            
            #line 33 "..\..\..\PleaseInsertNoteV2.xaml"
            this.Submit.Click += new System.Windows.RoutedEventHandler(this.Button_DepositSubmit);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BorderHelp = ((System.Windows.Controls.Border)(target));
            return;
            case 6:
            this.BtnHelp = ((System.Windows.Controls.Button)(target));
            
            #line 37 "..\..\..\PleaseInsertNoteV2.xaml"
            this.BtnHelp.Click += new System.Windows.RoutedEventHandler(this.Button_AddMore);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BackBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 8:
            this.Back = ((System.Windows.Controls.Button)(target));
            
            #line 43 "..\..\..\PleaseInsertNoteV2.xaml"
            this.Back.Click += new System.Windows.RoutedEventHandler(this.Button_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.CancelBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 10:
            this.Cancel = ((System.Windows.Controls.Button)(target));
            
            #line 47 "..\..\..\PleaseInsertNoteV2.xaml"
            this.Cancel.Click += new System.Windows.RoutedEventHandler(this.Button_Click_2);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

