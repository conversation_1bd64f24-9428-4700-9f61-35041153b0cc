﻿// Decompiled with JetBrains decompiler
// Type: MPOST.PauseDetectedEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("F133A487-26AE-4272-BE60-AA623F40FF3C")]
  [ComVisible(true)]
  public delegate void PauseDetectedEventHandler(object sender, EventArgs e);
}
