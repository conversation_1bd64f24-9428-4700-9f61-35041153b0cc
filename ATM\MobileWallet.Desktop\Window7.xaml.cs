﻿using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows;
using MobileWallet.Desktop.API;
using Newtonsoft.Json;

namespace MobileWallet.Desktop
{
    /// <summary>
    /// Interaction logic for Window2.xaml
    /// </summary>
    public partial class Window7 : Window
    {
        public Window7()
        {
            InitializeComponent();
        }
        private void Button_Click_2(object sender, RoutedEventArgs e)
        {
            SelectMobileMoney mainWindow = new SelectMobileMoney();
            mainWindow.Show();
            this.Close();
        }

        private void SetAccountNumber(string value)
        {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.Append(value);
            string finalvalue = stringBuilder.ToString();
            txtAccountNumber.Text += finalvalue;
        }



        private void btn2_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn2.Content.ToString());
        }

        private void btn3_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn3.Content.ToString());
        }

        private void btn4_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn4.Content.ToString());
        }

        private void btn5_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn5.Content.ToString());
        }

        private void btn6_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn6.Content.ToString());
        }

        private void btn7_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn7.Content.ToString());
        }

        private void btn8_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn8.Content.ToString());
        }

        private void btn9_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn9.Content.ToString());
        }

        private void btn0_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn0.Content.ToString());
        }

        private void btnremovenumber_Click(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(txtAccountNumber.Text))
            {
                int length = txtAccountNumber.Text.Length;
                string restnumbers = txtAccountNumber.Text.Substring(0, length - 1);
                txtAccountNumber.Text = restnumbers;

            }
        }
        private void btnclear_Click(object sender, RoutedEventArgs e)
        {
            txtAccountNumber.Text = string.Empty;
        }

        private void btn1_Click_1(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn1.Content.ToString());
        }

        private void Button_Click_Back(object sender, RoutedEventArgs e)
        {
            Window6 NewWindow = new Window6();
            NewWindow.Show();
            this.Close();
        }

        private void Button_Click_Cancel(object sender, RoutedEventArgs e)
        {
            WelcomeToMobileWallet NewWindow = new WelcomeToMobileWallet();
            NewWindow.Show();
            this.Close();
        }

        private void Button_Click_Resendotp(object sender, RoutedEventArgs e)
        {
            Window7 NewWindow = new Window7();
            NewWindow.Show();
            this.Close();
        }

        private void Button_Click_Submit(object sender, RoutedEventArgs e)
        {
            if (txtAccountNumber.Text.Length > 6 || txtAccountNumber.Text.Length < 6)
            {
                MessageBox.Show("Please enter 6 digit otp !!");
                txtAccountNumber.Focus();
            }
            else
            {
                //Todo Fix this
                // OtpVerificationStatusCheckResponse objOtpVerifCheck = new OtpVerificationStatusCheckResponse();
                // HttpClientHandler clientHandler = new HttpClientHandler();
                // string jsonData = JsonConvert.SerializeObject(txtAccountNumber.Text);
                // string otp = Regex.Replace(jsonData, @",(?=[^""]*""(?:[^""]*""[^""]*"")*[^""]*$)", string.Empty);
                // //string apiURL = "https://localhost:44372/api/Sms/OtpVerificationStatusCheck";
                // string apiURL = "https://mobilewallet-api.conveyor.cloud/api/Sms/OtpVerificationStatusCheck";
                //
                // clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };
                // using (var client1 = new HttpClient(clientHandler))
                // {
                //     var userDataModel = new OtpModel()
                //     {
                //         Otp = (txtAccountNumber.Text.Trim())
                //     };
                //
                //     var postTask = client1.PostAsJsonAsync<OtpModel>(apiURL, userDataModel);
                //     var responseAsString = postTask.Result.Content.ReadAsStringAsync();
                //     objOtpVerifCheck = JsonConvert.DeserializeObject<OtpVerificationStatusCheckResponse>(responseAsString.Result.Trim());
                //     if (objOtpVerifCheck.Status == "approved")
                //     {
                //         PleaseEnterAmount NewWindow = new PleaseEnterAmount();
                //         NewWindow.Show();
                //         this.Close();
                //     }
                //     else
                //     {
                //         Window7 NewWindow = new Window7();
                //         NewWindow.Show();
                //         this.Close();
                //         CustomMessageBox.Show("Otp validation failed! Please enter the correct Otp.");
                //     }
                // }
            }

        }
    }
}


