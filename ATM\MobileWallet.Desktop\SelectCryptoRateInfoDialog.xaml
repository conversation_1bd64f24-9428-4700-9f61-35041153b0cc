﻿<Window x:Class="MobileWallet.Desktop.SelectCryptoRateInfoDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        WindowStyle="None"
        mc:Ignorable="d"
        Title="SelectCryptoRateInfoDialog" Height="1600" Width="900" WindowState="Maximized">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="110" />
            <RowDefinition Height="110" />
            <RowDefinition Height="3*" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <Image Grid.Column="0" Grid.Row="0" Grid.RowSpan="4" Stretch="Fill" Source="images/back-drop.png" />
        <!-- Title -->
        <Image Grid.Row="0" Width="250" Source="./images/logo.png" />
        <!-- Content -->
        <TextBlock
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            TextWrapping="Wrap" TextAlignment="Center" Name="MyHeader" Text="Crypto Rate Info" Margin="0,0,0,0"
            Foreground="White" FontSize="35" FontWeight="Bold" Grid.Row="1" />
        <Grid
            Grid.Row="2"
            Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" /> <!-- Header -->
                <RowDefinition Height="Auto" /> <!-- Market -->
                <RowDefinition Height="Auto" /> <!-- Est. Average Price -->
                <RowDefinition Height="Auto" /> <!-- Est. Slippage -->
                <RowDefinition Height="Auto" /> <!-- Amount -->
                <RowDefinition Height="Auto" /> <!-- Payment method -->
                <RowDefinition Height="Auto" /> <!-- Subtotal -->
                <RowDefinition Height="Auto" /> <!-- Est. Fee -->
                <RowDefinition Height="Auto" /> <!-- Rebate (est.) -->
                <RowDefinition Height="Auto" /> <!-- Est. Total -->
                <RowDefinition Height="Auto" /> <!-- Quote -->
                <RowDefinition Height="Auto" /> <!-- Exchange rate -->
            </Grid.RowDefinitions>

            <!-- Market -->
            <Grid Grid.Row="1" Margin="0,5,0,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <TextBlock Name="TxtMarketLabel" Text="Market" Foreground="White" FontSize="35" FontWeight="Bold" />
                <TextBlock
                    Name="TxtMarketValue" Text="BTC-USD" Foreground="White" FontSize="35" FontWeight="Bold"
                    Grid.Column="1" TextAlignment="Right" />
            </Grid>

            <!-- Est. Average Price -->
            <Grid Grid.Row="2" Margin="0,5,0,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <TextBlock Name="TxtAveragePriceLabel" Text="Est. Average Price" Foreground="White" FontSize="35"
                           FontWeight="Bold" />
                <TextBlock Name="TxtAveragePriceValue" Text="$96,878.01" Foreground="White" FontSize="35"
                           FontWeight="Bold" Grid.Column="1" TextAlignment="Right" />
            </Grid>

            <!-- Est. Slippage -->
            <Grid Grid.Row="3" Margin="0,5,0,5" Visibility="Collapsed">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <TextBlock Name="TxtSlippageLabel" Text="Est. Slippage" Foreground="White" FontSize="35"
                           FontWeight="Bold" />
                <TextBlock Name="TxtSlippageValue" Text="0.01%" Foreground="White" FontSize="35" FontWeight="Bold"
                           Grid.Column="1" TextAlignment="Right" />
            </Grid>

            <!-- Amount -->
            <Grid Grid.Row="4" Margin="0,5,0,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <TextBlock Name="TxtAmountLabel" Text="Amount" Foreground="White" FontSize="35" FontWeight="Bold" />
                <TextBlock Name="TxtAmountValue" Text="0.00010200 BTC" Foreground="White" FontSize="35"
                           FontWeight="Bold" Grid.Column="1" TextAlignment="Right" />
            </Grid>


            <!-- Est. Fee -->
            <Grid Grid.Row="5" Margin="0,5,0,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <TextBlock Name="TxtFeeLabel" Text="Est. Fee" Foreground="White" FontSize="35" FontWeight="Bold" />
                <TextBlock Name="TxtFeeValue" Text="$0.12" Foreground="White" FontSize="35" FontWeight="Bold"
                           Grid.Column="1" TextAlignment="Right" />
            </Grid>
            <Grid Grid.Row="6" Margin="0,5,0,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <TextBlock Name="TxtQuoteLabel" Text="Quote" Foreground="White" FontSize="35" FontWeight="Bold" />
                <TextBlock Name="TxtQuoteValue" Text="TEST QUOTE" Foreground="White" FontSize="35" FontWeight="Bold"
                           Grid.Column="1" TextAlignment="Right" />
            </Grid>
            <!-- <Grid Grid.Row="7" Margin="0,5,0,5"> -->
            <!--     <Grid.ColumnDefinitions> -->
            <!--         <ColumnDefinition Width="*" /> -->
            <!--         <ColumnDefinition Width="Auto" /> -->
            <!--     </Grid.ColumnDefinitions> -->
            <!--     <TextBlock Name="TxtTotalLocalLabel" Text="Total Local" Foreground="White" FontSize="35" -->
            <!--                FontWeight="Bold" /> -->
            <!--     <TextBlock Name="TxtTotalLocalValue" Text="1000 XAF" Foreground="White" FontSize="35" FontWeight="Bold" -->
            <!--                Grid.Column="1" TextAlignment="Right" /> -->
            <!-- </Grid> -->
            <Grid Grid.Row="7" Margin="0,5,0,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <TextBlock Name="TxtExchangeRate" Text="Exchange Rate" Foreground="White" FontSize="35" FontWeight="Bold" />
                <TextBlock Name="TxtExchangeRateValue" Text="$ 1.00" Foreground="White" FontSize="35" FontWeight="Bold"
                           Grid.Column="1" TextAlignment="Right" />
            </Grid>
            <Grid Grid.Row="8" Margin="0,5,0,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <TextBlock Name="TxtTotalUsdLabel" Text="Total USD" Foreground="White" FontSize="35" FontWeight="Bold" />
                <TextBlock Name="TxtTotalUsdValue" Text="$ 1.00" Foreground="White" FontSize="35" FontWeight="Bold"
                           Grid.Column="1" TextAlignment="Right" />
            </Grid>
            

        </Grid>

        <StackPanel Orientation="Horizontal"
                    HorizontalAlignment="Center"
                    Name="BtnYes"
                    VerticalAlignment="Top"
                    Grid.Row="3" Margin="10">
            <Border
                VerticalAlignment="Top"
                HorizontalAlignment="Center"
                Margin="10" BorderBrush="#5387FC" BorderThickness="4" Width="100" Background="#ffffff" Height="100"
                CornerRadius="10">
                <Button Name="BtnOkContent" Margin="0 0 0 0" BorderThickness="0" Background="Transparent"
                        Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                        Canvas.ZIndex="2" Content="OK" Click="Yes_OnClick" />
            </Border>
        </StackPanel>
        <StackPanel Orientation="Horizontal"
                    HorizontalAlignment="Center"
                    Name="BtnNo"
                    VerticalAlignment="Top"
                    Grid.Row="3" Margin="10">
            <Border
                VerticalAlignment="Top"
                HorizontalAlignment="Center"
                Margin="10" BorderBrush="#5387FC" BorderThickness="4" Width="100" Background="#ffffff" Height="100"
                CornerRadius="10">
                <Button Name="BtnNoContent" Margin="0 0 0 0" BorderThickness="0" Background="Transparent"
                        Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                        Canvas.ZIndex="2" Content="No" Click="No_OnClick" />
            </Border>
            <Border
                VerticalAlignment="Top"
                HorizontalAlignment="Center"
                Margin="10" BorderBrush="#5387FC" BorderThickness="4" Width="100" Background="#ffffff" Height="100"
                CornerRadius="10">
                <Button Name="BtnYesContent" Margin="0 0 0 0" BorderThickness="0" Background="Transparent"
                        Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                        Canvas.ZIndex="2" Content="Yes" Click="Yes_OnClick" />
            </Border>
            
        </StackPanel>
    </Grid>
</Window>