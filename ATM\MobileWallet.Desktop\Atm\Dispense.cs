﻿using System.IO.Ports;

namespace MobileWallet.Desktop.Atm;

public class Dispense
{
    public string portName = "COM4";
    int baudRate = 9600;
    Parity parity = Parity.Even;
    SerialPort serialPort;

    // Initialize the hardware

    public string initialize()
    {
        serialPort = new SerialPort("COM4", 9600, parity);
        serialPort.Handshake = Handshake.None;
        serialPort.DataBits = 7;
        serialPort.StopBits = StopBits.One;
        serialPort.Open();
        return "success";
    }

    // Open Cassette

    public string open_cassette()
    {
        try
        {
            string command = LRCCalculate("8");
            serialPort.WriteLine(command + Environment.NewLine);

            bool check = false;

            string response = "";

            while (check == false)
            {
                response = serialPort.ReadExisting();
                if (response != "" && response != null)
                {
                    check = true;
                }
            }

            return manage_result(response[0]);
        }
        catch (Exception ex)
        {
            App.AppLogger.Error(ex,ex.Message);
            return ex.Message;
        }
    }

    // Close Cassette

    public string close_cassette()
    {
        try
        {
            string command = LRCCalculate("7");
            serialPort.WriteLine(command + Environment.NewLine);

            bool check = false;

            string response = "";

            while (check == false)
            {
                response = serialPort.ReadExisting();
                if (response != "" && response != null)
                {
                    check = true;
                }
            }

            return manage_result(response[0]);
        }
        catch (Exception ex)
        {
                App.AppLogger.Error(ex,ex.Message);
            return ex.Message;
        }
    }

    // Reset Hardware

    public string reset()
    {
        try
        {
            string command = LRCCalculate("0");
            serialPort.WriteLine(command + Environment.NewLine);

            bool check = false;

            string response = "";

            while (check == false)
            {
                response = serialPort.ReadExisting();
                if (response != "" && response != null)
                {
                    check = true;
                }
            }

            return manage_result(response[0]);
        }
        catch (Exception ex)
        {
                App.AppLogger.Error(ex,ex.Message);
            return ex.Message;
        }
    }

    // Take cash from cassettes 

    public string move_forward(string cassette1, string cassette2, string cassette3, string cassette4)
    {
        try
        {
            string commandString = "20";
            if (cassette1 != "0")
            {
                commandString = commandString + "1" + cassette1.PadLeft(3, '0');
            }

            if (cassette2 != "0")
            {
                commandString = commandString + "2" + cassette2.PadLeft(3, '0');
            }

            if (cassette3 != "0")
            {
                commandString = commandString + "3" + cassette3.PadLeft(3, '0');
            }

            if (cassette4 != "0")
            {
                commandString = commandString + "4" + cassette4.PadLeft(3, '0');
            }

            string command = LRCCalculate(commandString);
            serialPort.WriteLine(command + Environment.NewLine);

            bool check = false;

            string response = "";

            while (check == false)
            {
                response = serialPort.ReadExisting();
                if (response != "" && response != null)
                {
                    check = true;
                }
            }

            return manage_result(response[0]);
        }
        catch (Exception ex)
        {
                App.AppLogger.Error(ex,ex.Message);
            return ex.Message;
        }
    }

    // Deliver Notes

    public string deliver_notes()
    {
        try
        {
            string command = LRCCalculate("3");
            serialPort.WriteLine(command + Environment.NewLine);

            bool check = false;

            string response = "";

            while (check == false)
            {
                response = serialPort.ReadExisting();
                if (response != "" && response != null)
                {
                    check = true;
                }
            }

            return manage_result(response[0]);
        }
        catch (Exception ex)
        {
                App.AppLogger.Error(ex,ex.Message);
            return ex.Message;
        }
    }

    public string LRCCalculate(string message)
    {
        // Step 1: Calculate the "exclusive or" of all characters in the string
        byte xorResult = 0;
        foreach (char character in message)
        {
            xorResult ^= Convert.ToByte(character);
        }

        // Step 2: Divide the hexadecimal value by 0x10 and truncate the result
        byte y = (byte)(xorResult / 0x10);

        // Step 3: Calculate the "logical and" between the result and 0x0F
        byte z = (byte)(xorResult & 0x0F);

        // Step 4: Add 0x30 to the last two values to get L1 and L2
        byte l1 = (byte)(y | 0x30);
        byte l2 = (byte)(z | 0x30);

        // Concatenate the original message with L1 and L2
        string finalOutput = message + Convert.ToChar(l1) + Convert.ToChar(l2);

        return finalOutput;
    }

    public string manage_result(char response)
    {
        string result = "";
        if (response == '0')
        {
            result = "Successful Command";
        }
        else if (response == '1')
        {
            result = "Low Level";
        }
        else if (response == '2')
        {
            result = "Empty Cassette";
        }
        else if (response == '3')
        {
            result = "Machine not opened";
        }
        else if (response == '4')
        {
            result = "Rejected Notes";
        }
        else if (response == '5')
        {
            result = "Diverter Failure";
        }
        else if (response == '6')
        {
            result = "Failure to feed";
        }
        else if (response == '7')
        {
            result = "Transmission Error";
        }
        else if (response == '8')
        {
            result = "Illeg Com or Com Seq";
        }
        else if (response == '9')
        {
            result = "Jam in note qualifier";
        }
        else if (response == ':')
        {
            result = "NC not press or prop ins";
        }
        else if (response == '<')
        {
            result = "No notes retracted";
        }
        else if (response == '?')
        {
            result = "RV not Pres or Prop Ins";
        }
        else if (response == '@')
        {
            result = "Delivery Failure";
        }
        else if (response == 'A')
        {
            result = "Reject Failure";
        }
        else if (response == 'B')
        {
            result = "Too many notes req";
        }
        else if (response == 'C')
        {
            result = "Jam in note feeder transport";
        }
        else if (response == 'D')
        {
            result = "Reject vault almost full";
        }
        else if (response == 'E')
        {
            result = "Cassette internal failure";
        }
        else if (response == 'F')
        {
            result = "Main Motor Failure";
        }
        else if (response == 'G')
        {
            result = "Rejected Cheque";
        }
        else if (response == 'I')
        {
            result = "Note Qualifier Faulty";
        }
        else if (response == 'J')
        {
            result = "NF exit sensor failure";
        }
        else if (response == 'K')
        {
            result = "Shutter Failure";
        }
        else if (response == 'M')
        {
            result = "Notes in bundle output unit";
        }
        else if (response == 'N')
        {
            result = "Communications Timeout";
        }
        else if (response == 'P')
        {
            result = "Shutter Failure";
        }
        else if (response == 'Q')
        {
            result = "Cassette Not Identified";
        }
        else if (response == 'W')
        {
            result = "Error in throat";
        }
        else if (response == '[')
        {
            result = "Sensor Error";
        }
        else if (response == '\'')
        {
            result = "NMD Internal Failure";
        }
        else if (response == 'a')
        {
            result = "Cassette Lock Faulty";
        }
        else if (response == 'b')
        {
            result = "Error in note stacking area";
        }
        else if (response == 'c')
        {
            result = "Module need service";
        }
        else if (response == 'e')
        {
            result = "No Message to resend";
        }
        else if (response == 'p')
        {
            result = "Cassette Out Error";
        }

        return result;
    }
}