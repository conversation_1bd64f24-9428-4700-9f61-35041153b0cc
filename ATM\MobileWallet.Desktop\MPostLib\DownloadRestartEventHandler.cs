﻿// Decompiled with JetBrains decompiler
// Type: MPOST.DownloadRestartEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("6126942F-6DFC-4724-B207-E1162704CAB3")]
  [ComVisible(true)]
  public delegate void DownloadRestartEventHandler(object sender, EventArgs e);
}
