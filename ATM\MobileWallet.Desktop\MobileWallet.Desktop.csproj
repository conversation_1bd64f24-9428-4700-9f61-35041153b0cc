﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net8.0-windows</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <UseWPF>true</UseWPF>
        <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Emgu.CV.Bitmap" Version="4.9.0.5494" />
        <PackageReference Include="Emgu.CV.runtime.windows" Version="4.9.0.5494" />
        <PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="6.0.0" />
        <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.0" />
        <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.3.0" />
        <PackageReference Include="NLog" Version="6.0.2" />
        <PackageReference Include="NLog.Schema" Version="6.0.2" />
        <PackageReference Include="Python.Runtime" Version="2.7.9" />
        <PackageReference Include="Python.Runtime.Windows" Version="3.7.2" />
        <PackageReference Include="QRCoder" Version="1.6.0" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.3.0" />
        <PackageReference Include="System.IO.Ports" Version="9.0.0" />
        <PackageReference Include="ZXing.Net" Version="0.16.9" />
        <PackageReference Include="ZXing.Net.Bindings.EmguCV" Version="0.16.4" />
    </ItemGroup>

    <ItemGroup>
        <Compile Update="ResourceEnglish.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>ResourceEnglish.resx</DependentUpon>
        </Compile>
        <Compile Update="ResourceFrench.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>ResourceFrench.resx</DependentUpon>
        </Compile>
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Update="ResourceEnglish.resx">
            <Generator>ResXFileCodeGenerator</Generator>
            <LastGenOutput>ResourceEnglish.Designer.cs</LastGenOutput>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </EmbeddedResource>
        <EmbeddedResource Update="ResourceFrench.resx">
            <Generator>ResXFileCodeGenerator</Generator>
            <LastGenOutput>ResourceFrench.Designer.cs</LastGenOutput>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </EmbeddedResource>
    </ItemGroup>

    <ItemGroup>
        <Resource Include="images\back-drop.png">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <Resource Include="images\back-drop1.png">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <Resource Include="images\Cash_Acceptor.jpg">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <Resource Include="images\cut.png">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <Resource Include="images\loader.gif">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <None Include="images\loader1.gif">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <Resource Include="images\logo.png">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <Resource Include="images\logo1.png">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <Resource Include="images\mtn.png">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <Resource Include="images\mtn1.png">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <Resource Include="images\orange.png">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <Resource Include="images\scan.png">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <None Remove="images\mtn.jpg" />
        <Resource Include="images\mtn.jpg">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <None Remove="images\crypto.jpg" />
        <Resource Include="images\crypto.jpg">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <None Update="images\momomtn.bmp">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="images\momomtn_1bit.bmp">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\authentication.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\concrt140.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\libcrypto-3.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\libssl-3.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\MPOST.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\MRZParseUtil.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\Msprintsdk.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\MttekReaderAPI.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\openjp2.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\scanner.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\ucrtbase.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="app_serial.py">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="python37.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\DevCapture.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\DevCapture.lib">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\BarcodeDll.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\libcurl.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\libiconv.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\libzbar-0.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\mfc140.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\msvcp140.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\msvcr120.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\pdflib.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\sdtapi.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\vcruntime140.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\WltRS.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="images\400x200.bmp">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="images\mwt-logo.bmp">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\BmpToJpg.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\CP210xRuntime.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\cximage.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\cximagedt.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\DTBarReader.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\DtIDCard.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\dtImagedll.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\dtncnnocr.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\dtocr.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\DTQRReader.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\DTReader.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\est32.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\GetRoi.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\liblept173.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\mfc100.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\mfc100u.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\mfc140d.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\msvcp100.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\msvcp140d.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\msvcr100.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\opencv_world342.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\TermbDT.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\TWCamera.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\TWImagePro.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\TWRPassportAPI.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\ucrtbased.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\UnPack.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\vcruntime140d.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\WltRSdt.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\xbarcode417.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\XFaceDetection.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\XtImgSdk.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\tessdata\big5_st_mult.data">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\tessdata\big5st.data">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\tessdata\BIG5XVERT.data">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\tessdata\chndot.data">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\tessdata\dtchn.data">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\tessdata\dtmarkEX.data">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\tessdata\eng.traineddata">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\tessdata\mrz.data">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\tessdata\OCRv3_det.bin">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\tessdata\OCRv3_det.param">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\tessdata\passportxy.dat">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\tessdata\passportxyT.dat">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\tessdata\sfz_digital.data">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\tessdata\ump.traineddata">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\tessdata\vie.traineddata">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\tessdata\xfd.bin">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\ITLCashDevice.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AppLibs\ITLComms.dll">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="NLog.config">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
        <Page Update="Window10.xaml">
            <XamlRuntime>$(DefaultXamlRuntime)</XamlRuntime>
            <SubType>Designer</SubType>
        </Page>
        <Page Update="SelectCashOut.xaml">
            <XamlRuntime>$(DefaultXamlRuntime)</XamlRuntime>
        </Page>
        <Page Update="PleaseEnterAmount.xaml">
            <XamlRuntime>$(DefaultXamlRuntime)</XamlRuntime>
        </Page>
        <Page Update="SecureOTPhasbeensent.xaml">
            <XamlRuntime>$(DefaultXamlRuntime)</XamlRuntime>
            <SubType>Designer</SubType>
        </Page>
        <Page Update="EnterAccountNumber.xaml">
            <XamlRuntime>$(DefaultXamlRuntime)</XamlRuntime>
        </Page>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Capture\" />
        <Folder Include="ErrorLogs\" />
        <Folder Include="PassPort\" />
    </ItemGroup>

    <ItemGroup>
        <Resource Include="images\Back.jpg">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
    </ItemGroup>
    <ItemGroup>
        <Compile Update="ResourceEnglish.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>ResourceEnglish.resx</DependentUpon>
        </Compile>
        <Compile Update="ResourceFrench.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>ResourceFrench.resx</DependentUpon>
        </Compile>
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Update="ResourceEnglish.resx">
            <Generator>ResXFileCodeGenerator</Generator>
            <LastGenOutput>ResourceEnglish.Designer.cs</LastGenOutput>
        </EmbeddedResource>
        <EmbeddedResource Update="ResourceFrench.resx">
            <Generator>ResXFileCodeGenerator</Generator>
            <LastGenOutput>ResourceFrench.Designer.cs</LastGenOutput>
        </EmbeddedResource>
    </ItemGroup>

    <ItemGroup>
        <None Update="MPOST.dll">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Msprintsdk.dll">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="SampleVedio.mp4">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
      <Reference Include="ITLCashDevice">
        <HintPath>AppLibs\ITLCashDevice.dll</HintPath>
      </Reference>
      <Reference Include="ITLComms">
        <HintPath>AppLibs\ITLComms.dll</HintPath>
      </Reference>
    </ItemGroup>
</Project>
