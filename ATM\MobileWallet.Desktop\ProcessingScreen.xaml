﻿<Window x:Class="MobileWallet.Desktop.ProcessingScreen"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        WindowStyle="None"
        Loaded="Window_Loaded"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Title="ProcessingScreen" Height="1600" Width="900">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Image Grid.Column="0" Grid.Row="0" Stretch="Fill" Source="./images/back-drop.png "/>
        <Border Grid.Row="0" Background="#2F4C8E" HorizontalAlignment="Center" VerticalAlignment="Center" Padding="40 40 40 40">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <MediaElement
                    MediaEnded="LoaderImage_OnMediaEnded"
                    LoadedBehavior="Manual"
                    UnloadedBehavior="Manual"
                    x:Name="loaderImage" Source="/images/loader1.gif" Width="80" Margin="0 0 0 0" />
                <TextBlock Name="TransactionProcessing" HorizontalAlignment="Center" FontWeight="Bold" Foreground="#ffffff" FontSize="40" Width="auto" TextWrapping="Wrap" VerticalAlignment="Center" Text="Processing....." />
            </StackPanel>
        </Border>
    </Grid>
</Window>
