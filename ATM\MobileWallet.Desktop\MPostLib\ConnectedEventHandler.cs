﻿// Decompiled with JetBrains decompiler
// Type: MPOST.ConnectedEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("28905A85-8A2A-4875-99E1-D7AA6D60321E")]
  [ComVisible(true)]
  public delegate void ConnectedEventHandler(object sender, EventArgs e);
}
