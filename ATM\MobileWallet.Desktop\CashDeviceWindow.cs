﻿using System.Windows;
using ITL;
using ITL.Events;

namespace MobileWallet.Desktop;

public class CashDeviceWindow : Window
{
    public CashDeviceWindow() { }

    public List<string> CashEvents { get; set; } = new List<string>();

    public bool IsLoading { get; set; } = false;

    public void InitCashDevice()
    {
        if (!Global.UseV2 || !Global.UseHardware)
        {
            return;
        }

        UnInitCashDevice();
        var tempNewCashDevice = App.CashDevice;
        tempNewCashDevice.DeviceStateChangedEvent += DevStateChangedEventHandler;
        tempNewCashDevice.DeviceModuleStateChangedEvent += DevModuleStateChangedEventHandler;
        tempNewCashDevice.CashEvent += CashEventHandler;
        tempNewCashDevice.ReplenishEvent += ReplenishEventHandler;
        tempNewCashDevice.DispenserTransactionEvent += DispenserTransactionEventHandler;
        tempNewCashDevice.SetDenominationRouteFinishedEvent +=
            SetDenominationRouteFinishedEventHandler;
    }

    public void UnInitCashDevice()
    {
        if (!Global.UseV2 || !Global.UseHardware)
        {
            return;
        }
        var tempNewCashDevice = App.CashDevice;
        tempNewCashDevice.DeviceStateChangedEvent -= DevStateChangedEventHandler;
        tempNewCashDevice.DeviceModuleStateChangedEvent -= DevModuleStateChangedEventHandler;
        tempNewCashDevice.CashEvent -= CashEventHandler;
        tempNewCashDevice.ReplenishEvent -= ReplenishEventHandler;
        tempNewCashDevice.DispenserTransactionEvent -= DispenserTransactionEventHandler;
        tempNewCashDevice.SetDenominationRouteFinishedEvent -=
            SetDenominationRouteFinishedEventHandler;
    }

    public void StopService()
    {
        App.CashDevice.stopRunning();
    }

    public void StartService()
    {
        App.CashDevice.startRunning();
    }

    public void SendAck()
    {
        var r = App.CashDevice.SendCustomCommand([0x0A], 1);
        r = App.CashDevice.SendCustomCommand([0x07], 1);
        r = App.CashDevice.SendCustomCommand([0x57], 1);
    }

    private void SetDenominationRouteFinishedEventHandler(
        object sender,
        SetDenominationRouteFinishedArgs e
    )
    {
        Console.WriteLine(e.ToString());
        if (e.Result)
        {
            currentIndex++;
            if (currentIndex < keys.Count)
            {
                var m_currAssign = App.CashDevice.currencyAssignment;
                App.CashDevice.SetDenominationRouteSingleCurrency(
                    m_currAssign.First(p => p.Value == keys[currentIndex]).Value,
                    dict[keys[currentIndex]]
                );
            }
        }
    }

    protected virtual void DispenserTransactionEventHandler(
        object sender,
        DispenserTransactionArgs e
    )
    {
        Console.WriteLine(e.ToString());
        if (e.State == DispenserTransactionState.COMPLETED)
        {
            SendAck();
            // StopService();
        }
    }

    protected virtual void ReplenishEventHandler(object sender, ReplenishEventArgs e) { }

    protected virtual void CashEventHandler(object sender, CashEventArgs e)
    {
        Console.WriteLine(e.ToString());
        var data = e;
        if (data.EventType == CashEvent.ESCROW)
        {
            App.CashDevice.AcceptFromEscrow();
            CashEvents.Add(data.ToString());
        }

        if (data.EventType == CashEvent.STACKED)
        {
            // App.CashDevice.Set_Denomination_Level(new ValueCountryCode(e.Value, e.CountryCode), 1);
            // App.CashDevice.GetAllLevels();
            var denominations = App.CashDevice.currencyAssignment;
            foreach (var denomination in denominations)
            {
                Console.WriteLine(
                    denomination
                        + " - "
                        + denomination.Stored
                        + " - "
                        + denomination.StoredInCashbox
                );
            }
        }

        if (data.EventType == CashEvent.STORED)
        {
            var denominations = App.CashDevice.currencyAssignment;
            foreach (var denomination in denominations)
            {
                Console.WriteLine(
                    denomination
                        + " - "
                        + denomination.Stored
                        + " - "
                        + denomination.StoredInCashbox
                );
            }
        }
    }

    protected virtual void DevModuleStateChangedEventHandler(object sender) { }

    protected virtual void DevStateChangedEventHandler(object sender, DeviceStateEventArgs e)
    {
        Console.WriteLine(e.ToString());
        if (e.State == DeviceState.STOPPED)
        {
            StartService();
            IsLoading = true;
        }
        if (e.State == DeviceState.CONNECTED)
        {
            StartService();
            IsLoading = true;
        }

        if (e.State == DeviceState.REJECTED)
        {
            if (App.CashDevice.GetLastRejectCode())
            {
                Console.WriteLine(App.CashDevice.rejectCategory);
            }
        }
        if (e.State == DeviceState.STARTED)
        {
            SetInhibit();
            _ = SetRoutes();
            IsLoading = false;
            var denominations = App.CashDevice.currencyAssignment;
            foreach (var denomination in denominations)
            {
                Console.WriteLine(
                    denomination
                        + " - "
                        + denomination.Stored
                        + " - "
                        + denomination.StoredInCashbox
                );
            }
        }
    }

    private void SetInhibit()
    {
        return;
        var m_currAssign = App.CashDevice.currencyAssignment;
        foreach (var denomination in m_currAssign)
        {
            App.CashDevice.SetDenominationInhibit(denomination.ValueCountryCode, true);
        }
    }

    private int currentIndex = 0;
    private Dictionary<int, DenominationRoute> dict = new Dictionary<int, DenominationRoute>()
    {
        { 100, DenominationRoute.RECYCLER_4 },
        { 200, DenominationRoute.CASHBOX },
        { 500, DenominationRoute.RECYCLER_3 },
        { 1000, DenominationRoute.RECYCLER_2 },
        { 2000, DenominationRoute.RECYCLER_1 },
        { 5000, DenominationRoute.CASHBOX },
        { 10000, DenominationRoute.CASHBOX },
    };

    private List<int> keys = new List<int>();

    private async Task SetRoutes()
    {
        var m_currAssign = App.CashDevice.currencyAssignment;
        keys = dict.Keys.ToList();
        App.CashDevice.SetDenominationRouteSingleCurrency(
            m_currAssign.First(p => p.Value == keys[currentIndex]).Value,
            dict[keys[currentIndex]]
        );
    }
}
