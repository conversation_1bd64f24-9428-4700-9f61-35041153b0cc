﻿<Window x:Class="MobileWallet.Desktop.Window5"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="Window5" Height="450" Width="800">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="160*"/>
            <ColumnDefinition Width="161*"/>
            <ColumnDefinition Width="39*"/>
            <ColumnDefinition Width="213*"/>
            <ColumnDefinition Width="120*"/>
            <ColumnDefinition Width="107*"/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="82.04"/>
            <RowDefinition Height="31"/>
            <RowDefinition Height="36.96"/>
            <RowDefinition Height="30.04"/>
            <RowDefinition Height="46"/>
            <RowDefinition Height="73.96"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <Image Grid.Column="0" Grid.Row="0" Grid.RowSpan="7" Stretch="Fill" Source="./images/back-drop.png " Grid.ColumnSpan="6"/>
        <Image Grid.Row="0" Width="250" Source="./images/logo.png" Grid.ColumnSpan="3" Margin="115,0,48,0" Grid.Column="1" Grid.RowSpan="3" />

        <TextBlock HorizontalAlignment="Left" Grid.Row="3" Margin="69,20,0,0" FontWeight="Bold" Foreground="#ffffff" FontSize="50" TextWrapping="Wrap" Text="Enter Account Number " Grid.ColumnSpan="6" Grid.RowSpan="3" />


        <Border Margin="20,41,0,87" HorizontalAlignment="Left" Grid.Row="5" Grid.RowSpan="2" BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" Grid.Column="3" Grid.ColumnSpan="3" >
            <Button Margin="0 0 0 0" BorderThickness="0" Background="Transparent" Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                    Click="Button_Click"  >Next</Button>
        </Border>

        <Border Margin="20,41,0,87" HorizontalAlignment="Left" Grid.Row="5" Grid.RowSpan="2" BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" Grid.ColumnSpan="4" >
            <Button Margin="0 0 0 0" BorderThickness="0" Background="Transparent" Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                    Click="Button_Click"  >Back</Button>
        </Border>
        <Border Margin="20,150,0,-96" HorizontalAlignment="Left"  Grid.Row="6" BorderBrush="#FC5353" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" Grid.ColumnSpan="4" >
            <Button Margin="0 0 0 0" BorderThickness="0" Background="Transparent" FontWeight="DemiBold" Foreground="#FC5353" FontSize="35"
                    Click="Button_Click"  >Cancel</Button>
        </Border>
        <TextBox HorizontalAlignment="Left" Margin="20,10,0,0" Grid.Row="6" Text="121231231223" TextWrapping="Wrap" VerticalAlignment="Top" Width="440" Height="47" Grid.ColumnSpan="4" Grid.Column="1"  />
        <Button Content="1"  HorizontalAlignment="Left" Margin="136,59,0,0" Grid.Row="5" VerticalAlignment="Top" Height="85"  Width="88" Grid.RowSpan="2" Grid.ColumnSpan="2" />
    </Grid>
</Window>
