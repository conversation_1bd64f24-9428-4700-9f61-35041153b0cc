{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"MobileWallet.Desktop/1.0.0": {"dependencies": {"Emgu.CV.Bitmap": "4.9.0.5494", "Emgu.CV.runtime.windows": "4.9.0.5494", "Microsoft.AspNet.WebApi.Client": "6.0.0", "Microsoft.AspNetCore.SignalR.Client": "9.0.0", "Microsoft.IdentityModel.Tokens": "8.3.0", "NLog": "6.0.2", "NLog.Schema": "6.0.2", "Python.Runtime": "2.7.9", "Python.Runtime.Windows": "3.7.2", "QRCoder": "1.6.0", "System.IO.Ports": "9.0.0", "System.IdentityModel.Tokens.Jwt": "8.3.0", "ZXing.Net": "0.16.9", "ZXing.Net.Bindings.EmguCV": "0.16.4", "ITLCashDevice": "1.1.1.0", "ITLComms": "1.1.1.0"}, "runtime": {"MobileWallet.Desktop.dll": {}}}, "Emgu.CV/4.9.0.5494": {"dependencies": {"System.Drawing.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/netstandard2.0/Emgu.CV.dll": {"assemblyVersion": "4.9.0.5494", "fileVersion": "4.9.0.5494"}}}, "Emgu.CV.Bitmap/4.9.0.5494": {"dependencies": {"Emgu.CV": "4.9.0.5494", "System.Drawing.Common": "8.0.1"}, "runtime": {"lib/netstandard2.0/Emgu.CV.Bitmap.dll": {"assemblyVersion": "4.9.0.5494", "fileVersion": "4.9.0.5494"}}}, "Emgu.CV.runtime.windows/4.9.0.5494": {"dependencies": {"Emgu.CV": "4.9.0.5494", "Emgu.runtime.windows.msvc.rt.arm64": "19.39.33523", "Emgu.runtime.windows.msvc.rt.x64": "19.39.33523", "Emgu.runtime.windows.msvc.rt.x86": "19.39.33523"}, "runtimeTargets": {"runtimes/win-arm64/native/cvextern.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.9.0.5494"}, "runtimes/win-x64/native/cvextern.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.9.0.5494"}, "runtimes/win-x64/native/libusb-1.0.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/opencv_videoio_ffmpeg490_64.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "2023.12.0.0"}, "runtimes/win-x86/native/cvextern.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.9.0.5494"}, "runtimes/win-x86/native/libusb-1.0.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/opencv_videoio_ffmpeg490.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "2023.12.0.0"}}}, "Emgu.runtime.windows.msvc.rt.arm64/19.39.33523": {"runtimeTargets": {"runtimes/win-arm64/native/concrt140.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "14.38.33135.0"}, "runtimes/win-arm64/native/msvcp140.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "14.38.33135.0"}, "runtimes/win-arm64/native/msvcp140_1.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "14.38.33135.0"}, "runtimes/win-arm64/native/msvcp140_2.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "14.38.33135.0"}, "runtimes/win-arm64/native/msvcp140_atomic_wait.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "14.38.33135.0"}, "runtimes/win-arm64/native/msvcp140_codecvt_ids.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "14.38.33135.0"}, "runtimes/win-arm64/native/vcruntime140.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "14.38.33135.0"}, "runtimes/win-arm64/native/vcruntime140_1.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "14.38.33135.0"}}}, "Emgu.runtime.windows.msvc.rt.x64/19.39.33523": {"runtimeTargets": {"runtimes/win-x64/native/concrt140.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "14.38.33135.0"}, "runtimes/win-x64/native/msvcp140.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "14.38.33135.0"}, "runtimes/win-x64/native/msvcp140_1.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "14.38.33135.0"}, "runtimes/win-x64/native/msvcp140_2.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "14.38.33135.0"}, "runtimes/win-x64/native/msvcp140_atomic_wait.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "14.38.33135.0"}, "runtimes/win-x64/native/msvcp140_codecvt_ids.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "14.38.33135.0"}, "runtimes/win-x64/native/vcruntime140.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "14.38.33135.0"}, "runtimes/win-x64/native/vcruntime140_1.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "14.38.33135.0"}}}, "Emgu.runtime.windows.msvc.rt.x86/19.39.33523": {"runtimeTargets": {"runtimes/win-x86/native/concrt140.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "14.38.33135.0"}, "runtimes/win-x86/native/msvcp140.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "14.38.33135.0"}, "runtimes/win-x86/native/msvcp140_1.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "14.38.33135.0"}, "runtimes/win-x86/native/msvcp140_2.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "14.38.33135.0"}, "runtimes/win-x86/native/msvcp140_atomic_wait.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "14.38.33135.0"}, "runtimes/win-x86/native/msvcp140_codecvt_ids.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "14.38.33135.0"}, "runtimes/win-x86/native/vcruntime140.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "14.38.33135.0"}}}, "Microsoft.AspNet.WebApi.Client/6.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/System.Net.Http.Formatting.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.61130.707"}}}, "Microsoft.AspNetCore.Connections.Abstractions/9.0.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.0", "Microsoft.Extensions.Features": "9.0.0", "System.IO.Pipelines": "9.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.AspNetCore.Connections.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.Http.Connections.Client/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Http.Connections.Common": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "System.Net.ServerSentEvents": "9.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.AspNetCore.Http.Connections.Client.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.Http.Connections.Common/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "9.0.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Connections.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.SignalR.Client/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Http.Connections.Client": "9.0.0", "Microsoft.AspNetCore.SignalR.Client.Core": "9.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Client.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.SignalR.Client.Core/9.0.0": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "9.0.0", "Microsoft.AspNetCore.SignalR.Protocols.Json": "9.0.0", "Microsoft.Bcl.TimeProvider": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "System.Threading.Channels": "9.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.AspNetCore.SignalR.Client.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.SignalR.Common/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.SignalR.Protocols.Json/9.0.0": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "9.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.Bcl.AsyncInterfaces/9.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Bcl.TimeProvider/9.0.0": {"runtime": {"lib/net8.0/Microsoft.Bcl.TimeProvider.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Features/9.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.Features.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.Extensions.Logging/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "System.Diagnostics.DiagnosticSource": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Options/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Primitives/9.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.IdentityModel.Abstractions/8.3.0": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.3.0.51204"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.3.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.3.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.3.0.51204"}}}, "Microsoft.IdentityModel.Logging/8.3.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.3.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.3.0.51204"}}}, "Microsoft.IdentityModel.Tokens/8.3.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.3.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.3.0.51204"}}}, "Microsoft.NETCore.Platforms/1.1.1": {}, "Microsoft.NETCore.Targets/1.1.3": {}, "Microsoft.Win32.SystemEvents/8.0.0": {}, "NETStandard.Library/2.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1"}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.25517"}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.2.22727"}}}, "NLog/6.0.2": {"runtime": {"lib/netstandard2.1/NLog.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.2.4328"}}}, "NLog.Schema/6.0.2": {}, "Python.Runtime/2.7.9": {}, "Python.Runtime.Windows/3.7.2": {"dependencies": {"System.Reflection.Emit": "4.3.0"}, "runtime": {"lib/netstandard2.0/Python.Runtime.dll": {"assemblyVersion": "3.7.2.0", "fileVersion": "3.7.2.0"}}}, "QRCoder/1.6.0": {"dependencies": {"System.Drawing.Common": "8.0.1"}, "runtime": {"lib/net6.0-windows7.0/QRCoder.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.6.0.0"}}}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/android-arm/native/libSystem.IO.Ports.Native.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/android-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/android-x64/native/libSystem.IO.Ports.Native.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/android-x86/native/libSystem.IO.Ports.Native.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/linux-arm/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/linux-bionic-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-bionic-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/linux-bionic-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-bionic-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/linux-musl-arm/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/linux-musl-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/linux-musl-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/linux-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/maccatalyst-arm64/native/libSystem.IO.Ports.Native.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/maccatalyst-x64/native/libSystem.IO.Ports.Native.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.IO.Ports/9.0.0": {"dependencies": {"runtime.android-arm.runtime.native.System.IO.Ports": "9.0.0", "runtime.android-arm64.runtime.native.System.IO.Ports": "9.0.0", "runtime.android-x64.runtime.native.System.IO.Ports": "9.0.0", "runtime.android-x86.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-arm.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-arm64.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-bionic-x64.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-musl-arm.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-musl-arm64.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-musl-x64.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-x64.runtime.native.System.IO.Ports": "9.0.0", "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports": "9.0.0", "runtime.maccatalyst-x64.runtime.native.System.IO.Ports": "9.0.0", "runtime.osx-arm64.runtime.native.System.IO.Ports": "9.0.0", "runtime.osx-x64.runtime.native.System.IO.Ports": "9.0.0"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "System.Diagnostics.DiagnosticSource/9.0.0": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Drawing.Common/8.0.1": {"dependencies": {"Microsoft.Win32.SystemEvents": "8.0.0"}, "runtime": {"lib/net8.0/System.Drawing.Common.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.123.58006"}}}, "System.Drawing.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.IdentityModel.Tokens.Jwt/8.3.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.3.0", "Microsoft.IdentityModel.Tokens": "8.3.0"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "8.3.0.51204"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Pipelines/9.0.0": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.IO.Ports/9.0.0": {"dependencies": {"runtime.native.System.IO.Ports": "9.0.0"}, "runtime": {"lib/net8.0/System.IO.Ports.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/System.IO.Ports.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}, "runtimes/win/lib/net8.0/System.IO.Ports.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Memory/4.5.5": {}, "System.Net.ServerSentEvents/9.0.0": {"runtime": {"lib/net8.0/System.Net.ServerSentEvents.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Text.Encodings.Web/9.0.0": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Text.Json/9.0.0": {"dependencies": {"System.IO.Pipelines": "9.0.0", "System.Text.Encodings.Web": "9.0.0"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Channels/9.0.0": {"runtime": {"lib/net8.0/System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "ZXing.Net/0.16.9": {"runtime": {"lib/net7.0/zxing.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "ZXing.Net.Bindings.EmguCV/0.16.4": {"dependencies": {"Emgu.CV": "4.9.0.5494", "NETStandard.Library": "2.0.0", "ZXing.Net": "0.16.9"}, "runtime": {"lib/netstandard2.0/ZXing.EmguCV.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "ITLCashDevice/1.1.1.0": {"runtime": {"ITLCashDevice.dll": {"assemblyVersion": "1.1.1.0", "fileVersion": "1.1.1.0"}}}, "ITLComms/1.1.1.0": {"runtime": {"ITLComms.dll": {"assemblyVersion": "1.1.1.0", "fileVersion": "1.1.1.0"}}}}}, "libraries": {"MobileWallet.Desktop/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Emgu.CV/4.9.0.5494": {"type": "package", "serviceable": true, "sha512": "sha512-arnHdpnI14Ov98BmiWOsijAO3qvs2Z5+nBD2uavJI24CRTRSvkCXKZoMmhJVA48QdCdWO8HkO4wMqEezL4Bz/A==", "path": "emgu.cv/4.9.0.5494", "hashPath": "emgu.cv.4.9.0.5494.nupkg.sha512"}, "Emgu.CV.Bitmap/4.9.0.5494": {"type": "package", "serviceable": true, "sha512": "sha512-0ukJNIRXF5RXffF3ly0ujLtX0ojQGIHM7jmKkZuuvLSu2ubhVHZdZeqIBVa95/P7B6YDyC10vkGFhMSiFUeoOw==", "path": "emgu.cv.bitmap/4.9.0.5494", "hashPath": "emgu.cv.bitmap.4.9.0.5494.nupkg.sha512"}, "Emgu.CV.runtime.windows/4.9.0.5494": {"type": "package", "serviceable": true, "sha512": "sha512-sByLvyNCzQCScjKHLRm1NrghPUOClXRxVibCav3ou8scflQA28aWB1P+nBMjyBE1qIQ1CGA+h5UB9XGtBSf5xg==", "path": "emgu.cv.runtime.windows/4.9.0.5494", "hashPath": "emgu.cv.runtime.windows.4.9.0.5494.nupkg.sha512"}, "Emgu.runtime.windows.msvc.rt.arm64/19.39.33523": {"type": "package", "serviceable": true, "sha512": "sha512-idtjLwgIM9eSdOSv9vB320N+5ha647ByAD20cx9mQ5bf1/pqOPg4+YIVy2Z5Yh/S1DxfIfn8kWHA9QbtKjDeCQ==", "path": "emgu.runtime.windows.msvc.rt.arm64/19.39.33523", "hashPath": "emgu.runtime.windows.msvc.rt.arm64.19.39.33523.nupkg.sha512"}, "Emgu.runtime.windows.msvc.rt.x64/19.39.33523": {"type": "package", "serviceable": true, "sha512": "sha512-P6ibPhYDbQbDykWrgpvelZRjdc0tU6F6YcdRe3DEtKfdi5A+QXNuyg0HcMsUcQBvZMIVzezjohHhWbwamTDGLw==", "path": "emgu.runtime.windows.msvc.rt.x64/19.39.33523", "hashPath": "emgu.runtime.windows.msvc.rt.x64.19.39.33523.nupkg.sha512"}, "Emgu.runtime.windows.msvc.rt.x86/19.39.33523": {"type": "package", "serviceable": true, "sha512": "sha512-WwCxayYRgG+o7sZ16NEaIsOajRytEtjJ8/bnqKziomZahp9LkWUwxfeSvttAYSCKNFCMQUoDS90Fa0NaIm10Pg==", "path": "emgu.runtime.windows.msvc.rt.x86/19.39.33523", "hashPath": "emgu.runtime.windows.msvc.rt.x86.19.39.33523.nupkg.sha512"}, "Microsoft.AspNet.WebApi.Client/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zXeWP03dTo67AoDHUzR+/urck0KFssdCKOC+dq7Nv1V2YbFh/nIg09L0/3wSvyRONEdwxGB/ssEGmPNIIhAcAw==", "path": "microsoft.aspnet.webapi.client/6.0.0", "hashPath": "microsoft.aspnet.webapi.client.6.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Connections.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-s2di/tl/ZvLMOEa3KA3VZXmZT1QEefvn7fMCIbHuA/e2xP8/NbLz+q+D6FXuyR/A6koyOB4r88i+99Ya00XJiw==", "path": "microsoft.aspnetcore.connections.abstractions/9.0.0", "hashPath": "microsoft.aspnetcore.connections.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections.Client/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wuryRcgeSw022Hj0RObHh+P2K+Kk1ZjTrECnlHfMdY4PPqqGNYUnGV+MYZ5Q4jEdShpne0y4W6eBozxoG9wtRw==", "path": "microsoft.aspnetcore.http.connections.client/9.0.0", "hashPath": "microsoft.aspnetcore.http.connections.client.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections.Common/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hcJwfQ6bIxto8SUgnFd5wHi8pZ51zG1uHWr+YdKrgfBoAR8pWjYsqalJ5di71vDPMRvF8sdgC/KJA+HrxTLRAA==", "path": "microsoft.aspnetcore.http.connections.common/9.0.0", "hashPath": "microsoft.aspnetcore.http.connections.common.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Client/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yBkwPRFPVkSVJqk83DhnTC99iyJ14iSUjcFfbzbYB1pYB1VxbaqdEo7p0xFB/IcMo+ARD0lcZzXPHi+01e+bcg==", "path": "microsoft.aspnetcore.signalr.client/9.0.0", "hashPath": "microsoft.aspnetcore.signalr.client.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Client.Core/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-RRy3TkkS4C29s6rKvTwCyJKAvZ7ZPyBg1Lv4HVl1s9G4ZcPy0Gs+hXkAlVcvlo8+jdwd6JORuyaoKkPZloKUSQ==", "path": "microsoft.aspnetcore.signalr.client.core/9.0.0", "hashPath": "microsoft.aspnetcore.signalr.client.core.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Common/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZGKfgj8IrC3RZYAbPYCR3hj2UXCAjQrnaLM4IQcsjRXFW2W9Hipn9PC9y6o4qv8yIQjCYLSrge9iuCo3JC3s4Q==", "path": "microsoft.aspnetcore.signalr.common/9.0.0", "hashPath": "microsoft.aspnetcore.signalr.common.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Protocols.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-pgbKru6+hBoCnMtADdnDNVK/HPCNV84vrkVLvK3wteJPFmN3dHuefr4QGTTIvBAhw3o7KYs3X4hnwXMOfLMOqA==", "path": "microsoft.aspnetcore.signalr.protocols.json/9.0.0", "hashPath": "microsoft.aspnetcore.signalr.protocols.json.9.0.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-owmu2Cr3IQ8yQiBleBHlGk8dSQ12oaF2e7TpzwJKEl4m84kkZJjEY1n33L67Y3zM5jPOjmmbdHjbfiL0RqcMRQ==", "path": "microsoft.bcl.asyncinterfaces/9.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.9.0.0.nupkg.sha512"}, "Microsoft.Bcl.TimeProvider/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MD+1/wpOQ9XaLPrHDTJTnQ18GjwNF1GqUuAFFhCyIWXtkEpaBY7v+OyPgh1WGeFvbV9gh4jEr95hFIwECrw8TA==", "path": "microsoft.bcl.timeprovider/9.0.0", "hashPath": "microsoft.bcl.timeprovider.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MCPrg7v3QgNMr0vX4vzRXvkNGgLg8vKWX0nKCWUxu2uPyMsaRgiRc1tHBnbTcfJMhMKj2slE/j2M9oGkd25DNw==", "path": "microsoft.extensions.dependencyinjection/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+6f2qv2a3dLwd5w6JanPIPs47CxRbnk+ZocMJUhv9NxP88VlOcJYZs9jY+MYSjxvady08bUZn6qgiNh7DadGgg==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Features/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2mYnxoSho8W1e8VaZPr+mkeYaUi9XSwRK2M2t2w0dvQ06YTZ/1BZM274OMj4grH2GdQCe6mBn2vJDpmMInQdvA==", "path": "microsoft.extensions.features/9.0.0", "hashPath": "microsoft.extensions.features.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-crj<PERSON>yORoug0kK7RSNJBTeSE6VX8IQgLf3nUpTB9m62bPXp/tzbnOsnbe8TXEG0AASNaKZddnpHKw7fET8E++Pg==", "path": "microsoft.extensions.logging/9.0.0", "hashPath": "microsoft.extensions.logging.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-g0UfujELzlLbHoVG8kPKVBaW470Ewi+jnptGS9KUi6jcb+k2StujtK3m26DFSGGwQ/+bVgZfsWqNzlP6YOejvw==", "path": "microsoft.extensions.logging.abstractions/9.0.0", "hashPath": "microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-y2146b3jrPI3Q0lokKXdKLpmXqakYbDIPDV6r3M8SqvSf45WwOTzkyfDpxnZXJsJQEpAsAqjUq5Pu8RCJMjubg==", "path": "microsoft.extensions.options/9.0.0", "hashPath": "microsoft.extensions.options.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N3qEBzmLMYiASUlKxxFIISP4AiwuPTHF5uCh+2CWSwwzAJiIYx0kBJsS30cp1nvhSySFAVi30jecD307jV+8Kg==", "path": "microsoft.extensions.primitives/9.0.0", "hashPath": "microsoft.extensions.primitives.9.0.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jNin7yvWZu+K3U24q+6kD+LmGSRfbkHl9Px8hN1XrGwq6ZHgKGi/zuTm5m08G27fwqKfVXIWuIcUeq4Y1VQUOg==", "path": "microsoft.identitymodel.abstractions/8.3.0", "hashPath": "microsoft.identitymodel.abstractions.8.3.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-4SVXLT8sDG7CrHiszEBrsDYi+aDW0W9d+fuWUGdZPBdan56aM6fGXJDjbI0TVGEDjJhXbACQd8F/BnC7a+m2RQ==", "path": "microsoft.identitymodel.jsonwebtokens/8.3.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.3.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-4w4pSIGHhCCLTHqtVNR2Cc/zbDIUWIBHTZCu/9ZHm2SVwrXY3RJMcZ7EFGiKqmKZMQZJzA0bpwCZ6R8Yb7i5VQ==", "path": "microsoft.identitymodel.logging/8.3.0", "hashPath": "microsoft.identitymodel.logging.8.3.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yGzqmk+kInH50zeSEH/L1/J0G4/yqTQNq4YmdzOhpE7s/86tz37NS2YbbY2ievbyGjmeBI1mq26QH+yBR6AK3Q==", "path": "microsoft.identitymodel.tokens/8.3.0", "hashPath": "microsoft.identitymodel.tokens.8.3.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-TMBuzAHpTenGbGgk0SMTwyEkyijY/Eae4ZGsFNYJvAr/LDn1ku3Etp3FPxChmDp5HHF3kzJuoaa08N0xjqAJfQ==", "path": "microsoft.netcore.platforms/1.1.1", "hashPath": "microsoft.netcore.platforms.1.1.1.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "path": "microsoft.netcore.targets/1.1.3", "hashPath": "microsoft.netcore.targets.1.1.3.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9opKRyOKMCi2xJ7Bj7kxtZ1r9vbzosMvRrdEhVhDz8j8MoBGgB+WmC94yH839NPH+BclAjtQ/pyagvi/8gDLkw==", "path": "microsoft.win32.systemevents/8.0.0", "hashPath": "microsoft.win32.systemevents.8.0.0.nupkg.sha512"}, "NETStandard.Library/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7jnbRU+L08FXKMxqUflxEXtVymWvNOrS8yHgu9s6EM8Anr6T/wIX4nZ08j/u3Asz+tCufp3YVwFSEvFTPYmBPA==", "path": "netstandard.library/2.0.0", "hashPath": "netstandard.library.2.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "NLog/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-kfjfjcjh/hHXLJ0TbEUH6ajb2jQFmwk/23nyYW9iPZ6cj5769SyeDTbUwGI7LSVUk5iTRJoC6CTKKmWrXK79oA==", "path": "nlog/6.0.2", "hashPath": "nlog.6.0.2.nupkg.sha512"}, "NLog.Schema/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-jc570WC5BH3sO1JZzXssoojBEMhap9p4BzhYbpML1GC8i3eukSZAWDzPMEBcACerEICO/zqUUnfPQsrg9F7+hw==", "path": "nlog.schema/6.0.2", "hashPath": "nlog.schema.6.0.2.nupkg.sha512"}, "Python.Runtime/2.7.9": {"type": "package", "serviceable": true, "sha512": "sha512-NBS5t9KJvBVDoAFsVOQ79Ri133Hir8O6sqcEZJufXLVvlReXvzyZKhkcO5fpWcqjj6798qrWd/tO/TXCx2Ga7g==", "path": "python.runtime/2.7.9", "hashPath": "python.runtime.2.7.9.nupkg.sha512"}, "Python.Runtime.Windows/3.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-ZnQo1E1F1jNl7Ws7byi/dS78yQ3h7nWNNo7kVUCUojyhp25M2b1FAaz+mMBm/Scl+PeRwKinQiE1cmc+/+pnDQ==", "path": "python.runtime.windows/3.7.2", "hashPath": "python.runtime.windows.3.7.2.nupkg.sha512"}, "QRCoder/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-XmPA81eo+oRxBuyVdswsSkTGTE1d3thfF11Z1PdD7oB56A6HU4G4AAOdySmGRMb/cljwlFTMWUtosGEnwpS6GA==", "path": "qrcoder/1.6.0", "hashPath": "qrcoder.1.6.0.nupkg.sha512"}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zF8HT4aoFZkWF4OxhFLxUNEfoIjyILg0aQhgIR3m+dbLE4yadMd7kdctMvPhYYaVpnilmBCIjiQsrxH4UC/JxQ==", "path": "runtime.android-arm.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.android-arm.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JVRoxUTXhyFfDak3GLbZh9oPjz+eVJwiZQWOU/TQ1Nj7us11GMc97IBsRzjGDtGJvFOWhGhEkka8SYmVcwpA2A==", "path": "runtime.android-arm64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.android-arm64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QMWQv8nptbkzEDPUOmVwo3l/ve1pgApqv/eGY/eIJoNCGxUP6MYUu/GHdznRaBlSkuRyhFN8osVyqZMFKlBA7g==", "path": "runtime.android-x64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.android-x64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NRuTmUukSfpbv1wdJJXvWE/v1+aRHw5OxEODGeyKuFGy09uZIfFsdU1SPXB1cGPHsUaZRhZfOVel30zEgRQiUw==", "path": "runtime.android-x86.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.android-x86.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l5/3/3LfkemzovK66DrxsbGXRXIgmHaqYaYdhFR09lawWbPHhq4HJ0u2FzO+/neidm8bJtJAV6+iixMDuYIBgg==", "path": "runtime.linux-arm.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.linux-arm.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-q69FDpp5XSq3lJUMyMpUXBXTh6ekNM1NCnM5aYYiIx4AY1cH/rgLSwR4n2wQJqC6yuL0Z/epSf3KoYLYT8++Yg==", "path": "runtime.linux-arm64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.linux-arm64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kAOBq4UnR0B2UirRxLsPx4BIzt61Ydw40FFCe9NcFSncV6q+ikuhgN6eOrcaOcSu5QUiXacQRgFUX1Pux6ckYg==", "path": "runtime.linux-bionic-arm64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.linux-bionic-arm64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yCpRhte4+7C6ULKGA4qLaXGjQJwoygqyzgUN9u2tkfyGkwBUS66SRr6nNx522+4ATI8ZFkgIIZIkTczY77rcZw==", "path": "runtime.linux-bionic-x64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.linux-bionic-x64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-isaMOGqA4iIklwMt6wYTuPqj83D8DDUA2wainLqPjXaQ1Ri+5K8A+4J0BonjA/HMWtywBKnt2WGUXZ3DQN18ZA==", "path": "runtime.linux-musl-arm.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.linux-musl-arm.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yWsWQTf7r1aigde6EeoHzHhldoBw6fJ8AHR2ow4kobNuaS9Z/9rvLUFsGkAAY8GMUZadF5S1OGUsIzUd17RZBg==", "path": "runtime.linux-musl-arm64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.linux-musl-arm64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NxST2ZRBRGBjHWUnQwOYoyqFWHH4UcjAeTyjviSTOdwjSqq1JuGdp4sLzPzGDLiu4R7Per3QQ1GxYoLgAlIbOA==", "path": "runtime.linux-musl-x64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.linux-musl-x64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4bmb9oP1DIu2ArJ2MH2sNGbO5V3VrZ0+8lotr3cQ2G5hh66+0yHiYkwvlwP7gkSOsZPhANeX3cicqHYaDsroQA==", "path": "runtime.linux-x64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.linux-x64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-k+VeOPbIx9A1/bmiw5pGBsuALGTA4UoC6SsGhcIMLyS6TMFgsjsOH1bAgim+/W1RdtR7dpPCWHNYhkrM8hXByA==", "path": "runtime.maccatalyst-arm64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.maccatalyst-arm64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-k1WC+Y7ht+7Omq5iW1v2Yz5CpaGGlLvlNsGS8cDAG0IN3sXUrPyUkC/40/zTL8g8/c3UFjrW0igXcwKNYa+ZuA==", "path": "runtime.maccatalyst-x64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.maccatalyst-x64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iWyR+xohLUht80x5MREqF7zYD0KqyVpoS9uTg9raG0ddx5pvJkCPC4eS2JdkRYY6AqPjfMiiOEZ02ZWHEBgOvg==", "path": "runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ebr6uFzuICKkw9YePnCo7CdZFKYYhJZOJDJhACAKyzbT5WFvJWMyeACJIWS0uqndGMgWSc+D+UDdBu6CEpUOSg==", "path": "runtime.osx-arm64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.osx-arm64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-66DA4FKnfIdrkyd8Kqym06s+F/U5/7TZdkV1DllGivUNUGkC8TG5W/3D4rhLoGQRjg0uurkPWqrQXWfPEghRpQ==", "path": "runtime.osx-x64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.osx-x64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ddppcFpnbohLWdYKr/ZeLZHmmI+DXFgZ3Snq+/E7SwcdW4UnvxmaugkwGywvGVWkHPGCSZjCP+MLzu23AL5SDw==", "path": "system.diagnostics.diagnosticsource/9.0.0", "hashPath": "system.diagnostics.diagnosticsource.9.0.0.nupkg.sha512"}, "System.Drawing.Common/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-x0rAZECxIGx/YVjN28YRdpqka0+H7YMN9741FUDzipXPDzesd60gef/LI0ZCOcYSDsacTLTHvMAvxHG+TjbNNQ==", "path": "system.drawing.common/8.0.1", "hashPath": "system.drawing.common.8.0.1.nupkg.sha512"}, "System.Drawing.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1QU/c35gwdhvj77fkScXQQbjiVAqIL3fEYn/19NE0CV/ic5TN5PyWAft8HsrbRd4SBLEoErNCkWSzMDc0MmbRw==", "path": "system.drawing.primitives/4.3.0", "hashPath": "system.drawing.primitives.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9GESpDG0Zb17HD5mBW/uEWi2yz/uKPmCthX2UhyLnk42moGH2FpMgXA2Y4l2Qc7P75eXSUTA6wb/c9D9GSVkzw==", "path": "system.identitymodel.tokens.jwt/8.3.0", "hashPath": "system.identitymodel.tokens.jwt.8.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eA3cinogwaNB4jdjQHOP3Z3EuyiDII7MT35jgtnsA4vkn0LUrrSHsU0nzHTzFzmaFYeKV7MYyMxOocFzsBHpTw==", "path": "system.io.pipelines/9.0.0", "hashPath": "system.io.pipelines.9.0.0.nupkg.sha512"}, "System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfEWew48r4MxHUnOQL7nw/5JBsz9dli8TJYpXjsAQu8tHH0QCq2ly4QMCc8wS9EAi1jvaFgq7ELdfwxvrKWALQ==", "path": "system.io.ports/9.0.0", "hashPath": "system.io.ports.9.0.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Net.ServerSentEvents/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VTWjeyx9nPb4+hkjGcAaDw1nOckypMtvABmxSWm6PPYwrXoIiVG3jwtNlAGhaGVjDkBrERABox67wYTAcHxg7Q==", "path": "system.net.serversentevents/9.0.0", "hashPath": "system.net.serversentevents.9.0.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "path": "system.runtime/4.3.1", "hashPath": "system.runtime.4.3.1.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-e2hMgAErLbKyUUwt18qSBf9T5Y+SFAL3ZedM8fLupkVj8Rj2PZ9oxQ37XX2LF8fTO1wNIxvKpihD7Of7D/NxZw==", "path": "system.text.encodings.web/9.0.0", "hashPath": "system.text.encodings.web.9.0.0.nupkg.sha512"}, "System.Text.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-js7+qAu/9mQvnhA4EfGMZNEzXtJCDxgkgj8ohuxq/Qxv+R56G+ljefhiJHOxTNiw54q8vmABCWUwkMulNdlZ4A==", "path": "system.text.json/9.0.0", "hashPath": "system.text.json.9.0.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Channels/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hzACdIf1C+4Dqos5ijV404b94+LqfIC8nfS3mNpCDFWowb1N3PNfJPopneq32ahWlDeyaPZJqjBk76YFR69Rpg==", "path": "system.threading.channels/9.0.0", "hashPath": "system.threading.channels.9.0.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "ZXing.Net/0.16.9": {"type": "package", "serviceable": true, "sha512": "sha512-7WaVMHklpT3Ye2ragqRIwlFRsb6kOk63BOGADV0fan3ulVfGLUYkDi5yNUsZS/7FVNkWbtHAlDLmu4WnHGfqvQ==", "path": "zxing.net/0.16.9", "hashPath": "zxing.net.0.16.9.nupkg.sha512"}, "ZXing.Net.Bindings.EmguCV/0.16.4": {"type": "package", "serviceable": true, "sha512": "sha512-Zyb2BXh9KwKPs8aqgYe0KKti5c6Bzn13y0HD9unjqoffdnpDF/ejk4FaRc8w/Rc7kXdN6Rt+JjVY22pUHMcO6w==", "path": "zxing.net.bindings.emgucv/0.16.4", "hashPath": "zxing.net.bindings.emgucv.0.16.4.nupkg.sha512"}, "ITLCashDevice/1.1.1.0": {"type": "reference", "serviceable": false, "sha512": ""}, "ITLComms/1.1.1.0": {"type": "reference", "serviceable": false, "sha512": ""}}}