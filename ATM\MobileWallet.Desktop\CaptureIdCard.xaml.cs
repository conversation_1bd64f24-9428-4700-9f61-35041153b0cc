﻿using System.ComponentModel;
using System.Windows;
using System.Windows.Media.Imaging;
using System.Windows.Threading;
using Emgu.CV;
using Emgu.CV.CvEnum;
using MobileWallet.Desktop.API;
using MobileWallet.Desktop.Extensions;

namespace MobileWallet.Desktop;

public partial class CaptureIdCard : Window
{
    private VideoCapture? _capture;
    private DispatcherTimer? _timer;
    private bool _isRetake = false;

    public CaptureIdCard()
    {
        InitializeComponent();
        SetLanguage();
        this.SubmitBorder.Visibility = Visibility.Collapsed;
        if (Global.UseCamera)
        {
            StartCamera();
        }
    }

    private void SetLanguage()
    {
        if (Global.IsFrench)
        {
            BtnBack.Content = ResourceFrench.Back;
            BtnCancel.Content = ResourceFrench.Cancel;
            BtnCapture.Content = ResourceFrench.Capture;
            BtnSubmit.Content = ResourceFrench.Submit;
            WindowTitle.Text = ResourceFrench.PlaceIdCardInsideCamera;
        }
        else
        {
            BtnBack.Content = ResourceEnglish.Back;
            BtnCancel.Content = ResourceEnglish.Cancel;
            BtnCapture.Content = ResourceEnglish.Capture;
            BtnSubmit.Content = ResourceEnglish.Submit;
            WindowTitle.Text = ResourceEnglish.PlaceIdCardInsideCamera;
        }
    }

    private void Button_Click_Capture(object sender, RoutedEventArgs e)
    {
        if (_isRetake)
        {
            if (Global.UseCamera)
            {
                StartCamera();
            }
            _isRetake = false;
            this.SubmitBorder.Visibility = Visibility.Collapsed;
            if (Global.IsFrench)
            {
                this.BtnCapture.Content = ResourceFrench.Capture;
            }
            else
            {
                this.BtnCapture.Content = ResourceEnglish.Capture;
            }
        }
        else
        {
            if (Global.UseCamera)
            {
                StopCamera();
                App.SaveCameraImage();
            }
            this.SubmitBorder.Visibility = Visibility.Visible;
            if (Global.IsFrench)
            {
                this.BtnCapture.Content = ResourceFrench.RetakeImage;
            }
            else
            {
                this.BtnCapture.Content = ResourceEnglish.RetakeImage;
            }
            _isRetake = true;
        }
    }

    private void Button_Click_Cancel(object sender, RoutedEventArgs e)
    {
        _ = App.LogError("Cancel Button Pressed on Camera Page", LogType.Cancel);
        WelcomeToMobileWallet NewWindow = new WelcomeToMobileWallet();
        NewWindow.Show();
        this.Close();
    }

    private void Button_Click(object sender, RoutedEventArgs e)
    {
        _ = App.LogError("Back Button Pressed on Camera Page", LogType.Back);
        SelectYourID NewWindow = new SelectYourID();
        NewWindow.Show();
        this.Close();
    }

    private void Button_Click_Submit(object sender, RoutedEventArgs e)
    {
        if (Global.UseCamera)
        {
            _ = App.UploadCameraPictureIfExist();
        }

        if (Global.IsCrypto)
        {
            SelectCryptoNetwork window = new SelectCryptoNetwork();
            window.Show();
        }
        else
        {
            EnterAccountNumber window = new EnterAccountNumber();
            window.Show();
        }
        Close();
    }

    private void StartCamera()
    {
        try
        {
            if (_capture == null)
            {
                _capture = new VideoCapture(Global.CameraIndex, VideoCapture.API.DShow);
                _capture.Set(CapProp.Fps, 30); // Optional: Set FPS
                _capture.Set(CapProp.FrameWidth, 640); // Optional: Set Width
                _capture.Set(CapProp.FrameHeight, 480); // Optional: Set Height
            }

            if (_timer == null)
            {
                _timer = new DispatcherTimer
                {
                    Interval = TimeSpan.FromMilliseconds(
                        33
                    ) // ~30 FPS
                    ,
                };
                _timer.Tick += UpdateFrame;
            }
            _timer.Start();
        }
        catch (Exception ex)
        {
                App.AppLogger.Error(ex,ex.Message);
            MessageBox.Show($"Error initializing camera: {ex.Message}");
        }
    }

    private void UpdateFrame(object? sender, EventArgs e)
    {
        if (_capture != null && _capture.IsOpened)
        {
            using (Mat frame = _capture.QueryFrame())
            {
                if (frame != null && !frame.IsEmpty)
                {
                    CameraImage.Source = frame.ToBitmapSource();
                }
            }
        }
    }

    private void StopCamera()
    {
        Dispatcher.Invoke(() =>
        {
            try
            {
                _timer?.Stop();
                _capture?.Dispose();
                _timer = null;
                _capture = null;
            }
            catch (Exception e)
            {
                App.AppLogger.Error(e,e.Message);
                Console.WriteLine(e);
            }
        });
    }

    private void CaptureIdCard_OnClosing(object? sender, CancelEventArgs e)
    {
        App.StopTimer();
        StopCamera();
    }

    private void CaptureIdCard_OnLoaded(object sender, RoutedEventArgs e)
    {
        Helper.AdjustRowHeight(this, DynamicRow);
        App.StartTimer(this);
        _ = App.TrackAtmRealTime(
            new UpdateAtmRealTimeRequestModel() { CurrentScreen = nameof(CaptureIdCard) }
        );
    }
}
