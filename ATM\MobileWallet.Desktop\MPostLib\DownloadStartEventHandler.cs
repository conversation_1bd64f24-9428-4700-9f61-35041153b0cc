﻿// Decompiled with JetBrains decompiler
// Type: MPOST.DownloadStartEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("BFB0AA6A-48AD-484b-93DA-2A21942B1729")]
  [ComVisible(true)]
  public delegate void DownloadStartEventHandler(object sender, AcceptorDownloadEventArgs e);
}
