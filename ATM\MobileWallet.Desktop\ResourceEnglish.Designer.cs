﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace MobileWallet.Desktop {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class ResourceEnglish {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResourceEnglish() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("MobileWallet.Desktop.ResourceEnglish", typeof(ResourceEnglish).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amount.
        /// </summary>
        internal static string Amount {
            get {
                return ResourceManager.GetString("Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Back.
        /// </summary>
        internal static string Back {
            get {
                return ResourceManager.GetString("Back", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clear All.
        /// </summary>
        internal static string btnclear {
            get {
                return ResourceManager.GetString("btnclear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Backspace.
        /// </summary>
        internal static string btndone {
            get {
                return ResourceManager.GetString("btndone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Next.
        /// </summary>
        internal static string btnnext {
            get {
                return ResourceManager.GetString("btnnext", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy.
        /// </summary>
        internal static string Buy {
            get {
                return ResourceManager.GetString("Buy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy above 10,000 XAF.
        /// </summary>
        internal static string BuyAboveLimit {
            get {
                return ResourceManager.GetString("BuyAboveLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy above $500.
        /// </summary>
        internal static string BuyAboveLimitV2 {
            get {
                return ResourceManager.GetString("BuyAboveLimitV2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy below 10,000 XAF.
        /// </summary>
        internal static string BuyBelowLimit {
            get {
                return ResourceManager.GetString("BuyBelowLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy below $500.
        /// </summary>
        internal static string BuyBelowLimitV2 {
            get {
                return ResourceManager.GetString("BuyBelowLimitV2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        internal static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Capture.
        /// </summary>
        internal static string Capture {
            get {
                return ResourceManager.GetString("Capture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clear.
        /// </summary>
        internal static string Clear {
            get {
                return ResourceManager.GetString("Clear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Account Number.
        /// </summary>
        internal static string Confirm_Account_Number {
            get {
                return ResourceManager.GetString("Confirm Account Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Crypto.
        /// </summary>
        internal static string Crypto {
            get {
                return ResourceManager.GetString("Crypto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deposit.
        /// </summary>
        internal static string Deposit {
            get {
                return ResourceManager.GetString("Deposit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Done.
        /// </summary>
        internal static string Done {
            get {
                return ResourceManager.GetString("Done", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Driving License.
        /// </summary>
        internal static string DrivingLicence {
            get {
                return ResourceManager.GetString("DrivingLicence", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to English.
        /// </summary>
        internal static string English {
            get {
                return ResourceManager.GetString("English", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Mobile Number.
        /// </summary>
        internal static string EnterAccountNo {
            get {
                return ResourceManager.GetString("EnterAccountNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Enter Amount (between 5000 Fcfa and 200,000 Fcfa)..
        /// </summary>
        internal static string EnterAmount {
            get {
                return ResourceManager.GetString("EnterAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Enter Amount (between $5 and $5,000).
        /// </summary>
        internal static string EnterAmountUsd {
            get {
                return ResourceManager.GetString("EnterAmountUsd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Average Price.
        /// </summary>
        internal static string EstAveragePrice {
            get {
                return ResourceManager.GetString("EstAveragePrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fee.
        /// </summary>
        internal static string EstFee {
            get {
                return ResourceManager.GetString("EstFee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slippage.
        /// </summary>
        internal static string EstSlippage {
            get {
                return ResourceManager.GetString("EstSlippage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exchange Rate.
        /// </summary>
        internal static string ExchangeRate {
            get {
                return ResourceManager.GetString("ExchangeRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to French.
        /// </summary>
        internal static string French {
            get {
                return ResourceManager.GetString("French", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How much do you want to buy?.
        /// </summary>
        internal static string HowMuchDoYouWantToBuy {
            get {
                return ResourceManager.GetString("HowMuchDoYouWantToBuy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How much do you want to sell?.
        /// </summary>
        internal static string HowMuchDoYouWantToSell {
            get {
                return ResourceManager.GetString("HowMuchDoYouWantToSell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New ID Card.
        /// </summary>
        internal static string IDCard {
            get {
                return ResourceManager.GetString("IDCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Enter Account Number.
        /// </summary>
        internal static string lblMsg {
            get {
                return ResourceManager.GetString("lblMsg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Enter Amount.
        /// </summary>
        internal static string lblMsgEnterAmount {
            get {
                return ResourceManager.GetString("lblMsgEnterAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter the OTP value:.
        /// </summary>
        internal static string lblMsgOTP {
            get {
                return ResourceManager.GetString("lblMsgOTP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Market.
        /// </summary>
        internal static string Market {
            get {
                return ResourceManager.GetString("Market", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Momo.
        /// </summary>
        internal static string MtnMoney {
            get {
                return ResourceManager.GetString("MtnMoney", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Orange.
        /// </summary>
        internal static string OrangeMoney {
            get {
                return ResourceManager.GetString("OrangeMoney", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Others.
        /// </summary>
        internal static string Others {
            get {
                return ResourceManager.GetString("Others", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ATM is out of Service.
        /// </summary>
        internal static string OutOfService {
            get {
                return ResourceManager.GetString("OutOfService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Old Id Card/Passport.
        /// </summary>
        internal static string Passport {
            get {
                return ResourceManager.GetString("Passport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Payment Method.
        /// </summary>
        internal static string PaymentMethod {
            get {
                return ResourceManager.GetString("PaymentMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Place Your Card Align With The Camera.
        /// </summary>
        internal static string PlaceCard {
            get {
                return ResourceManager.GetString("PlaceCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Place your Id card towards the camera.
        /// </summary>
        internal static string PlaceIdCardInsideCamera {
            get {
                return ResourceManager.GetString("PlaceIdCardInsideCamera", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Place your passport inside the scanner.
        /// </summary>
        internal static string PlacePassportInsideCamera {
            get {
                return ResourceManager.GetString("PlacePassportInsideCamera", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please scan your wallet address below using the QR Code Scanner.
        /// </summary>
        internal static string PlaceYourQrCodeAddress {
            get {
                return ResourceManager.GetString("PlaceYourQrCodeAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please scan your transaction Id below using the QR Code Scanner.
        /// </summary>
        internal static string PlaceYourQrCodeAlignWithTheCamera {
            get {
                return ResourceManager.GetString("PlaceYourQrCodeAlignWithTheCamera", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter amount you want to deposit.
        /// </summary>
        internal static string PleaseEnterAmountDeposit {
            get {
                return ResourceManager.GetString("PleaseEnterAmountDeposit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter amount you want to withdraw.
        /// </summary>
        internal static string PleaseEnterAmountWithdraw {
            get {
                return ResourceManager.GetString("PleaseEnterAmountWithdraw", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to you will receive.
        /// </summary>
        internal static string QuoteDeposit {
            get {
                return ResourceManager.GetString("QuoteDeposit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to withdrawn.
        /// </summary>
        internal static string QuoteWithdraw {
            get {
                return ResourceManager.GetString("QuoteWithdraw", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resend OTP.
        /// </summary>
        internal static string ResendOTP {
            get {
                return ResourceManager.GetString("ResendOTP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resume.
        /// </summary>
        internal static string Resume {
            get {
                return ResourceManager.GetString("Resume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Retake Image.
        /// </summary>
        internal static string RetakeImage {
            get {
                return ResourceManager.GetString("RetakeImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Secure OTP has been sent to your register no..
        /// </summary>
        internal static string SecureOTPSend {
            get {
                return ResourceManager.GetString("SecureOTPSend", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Secure OTP has been sent to your wallet {Phone}\nName: {Name}.
        /// </summary>
        internal static string SecureOTPSendWithName {
            get {
                return ResourceManager.GetString("SecureOTPSendWithName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Secure OTP has been sent to your Phone: {Phone}.
        /// </summary>
        internal static string SecureOTPSendWithPhone {
            get {
                return ResourceManager.GetString("SecureOTPSendWithPhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Amount.
        /// </summary>
        internal static string Select_Amount {
            get {
                return ResourceManager.GetString("Select Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Cash Out.
        /// </summary>
        internal static string Select_Cash_Out {
            get {
                return ResourceManager.GetString("Select Cash Out", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Your ID &amp; Insert To The Scanner Area.
        /// </summary>
        internal static string Select_Your_ID___Insert_To_The_Scanner_Area {
            get {
                return ResourceManager.GetString("Select Your ID & Insert To The Scanner Area", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Your ID and Insert To The Scanner Area.
        /// </summary>
        internal static string Select_Your_ID_and_Insert_To_The_Scanner_Area {
            get {
                return ResourceManager.GetString("Select Your ID and Insert To The Scanner Area", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Cash In / Out.
        /// </summary>
        internal static string SelectCashInOut {
            get {
                return ResourceManager.GetString("SelectCashInOut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Crypto Network.
        /// </summary>
        internal static string SelectCryptoNetwork {
            get {
                return ResourceManager.GetString("SelectCryptoNetwork", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Crypto Token.
        /// </summary>
        internal static string SelectCryptoToken {
            get {
                return ResourceManager.GetString("SelectCryptoToken", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Identification Method.
        /// </summary>
        internal static string SelectIdentificationMethod {
            get {
                return ResourceManager.GetString("SelectIdentificationMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Language.
        /// </summary>
        internal static string SelectLaunguage {
            get {
                return ResourceManager.GetString("SelectLaunguage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Operator.
        /// </summary>
        internal static string SelectMoneyOperator {
            get {
                return ResourceManager.GetString("SelectMoneyOperator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell.
        /// </summary>
        internal static string Sell {
            get {
                return ResourceManager.GetString("Sell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell above 10,000 XAF.
        /// </summary>
        internal static string SellAboveLimit {
            get {
                return ResourceManager.GetString("SellAboveLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell above $500.
        /// </summary>
        internal static string SellAboveLimitV2 {
            get {
                return ResourceManager.GetString("SellAboveLimitV2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell below 10,000 XAF.
        /// </summary>
        internal static string SellBelowLimit {
            get {
                return ResourceManager.GetString("SellBelowLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell below $500.
        /// </summary>
        internal static string SellBelowLimitV2 {
            get {
                return ResourceManager.GetString("SellBelowLimitV2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submit.
        /// </summary>
        internal static string Submit {
            get {
                return ResourceManager.GetString("Submit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thank You . . ..
        /// </summary>
        internal static string Thanks {
            get {
                return ResourceManager.GetString("Thanks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Local.
        /// </summary>
        internal static string TotalLocal {
            get {
                return ResourceManager.GetString("TotalLocal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total USD.
        /// </summary>
        internal static string TotalUsd {
            get {
                return ResourceManager.GetString("TotalUsd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Processing Transaction....
        /// </summary>
        internal static string TransactionProcessing {
            get {
                return ResourceManager.GetString("TransactionProcessing", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Authenticating....
        /// </summary>
        internal static string Authenticating {
            get {
                return ResourceManager.GetString("Authenticating", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Please wait....
        /// </summary>
        internal static string PleaseWait {
            get {
                return ResourceManager.GetString("PleaseWait", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Verifying OTP....
        /// </summary>
        internal static string VerifyingOTP {
            get {
                return ResourceManager.GetString("VerifyingOTP", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Loading tokens....
        /// </summary>
        internal static string LoadingTokens {
            get {
                return ResourceManager.GetString("LoadingTokens", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Fetching rates....
        /// </summary>
        internal static string FetchingRates {
            get {
                return ResourceManager.GetString("FetchingRates", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Loading quotations....
        /// </summary>
        internal static string LoadingQuotations {
            get {
                return ResourceManager.GetString("LoadingQuotations", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Creating session....
        /// </summary>
        internal static string CreatingSession {
            get {
                return ResourceManager.GetString("CreatingSession", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Verifying address....
        /// </summary>
        internal static string VerifyingAddress {
            get {
                return ResourceManager.GetString("VerifyingAddress", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Processing request....
        /// </summary>
        internal static string ProcessingRequest {
            get {
                return ResourceManager.GetString("ProcessingRequest", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Loading data....
        /// </summary>
        internal static string LoadingData {
            get {
                return ResourceManager.GetString("LoadingData", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Verify Crypto Rates.
        /// </summary>
        internal static string VerifyCryptoRates {
            get {
                return ResourceManager.GetString("VerifyCryptoRates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Verifying your details, please wait....
        /// </summary>
        internal static string Verifying_your_details__please_wait___ {
            get {
                return ResourceManager.GetString("Verifying your details, please wait...", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Verifying your document, please wait....
        /// </summary>
        internal static string Verifying_your_document__please_wait___ {
            get {
                return ResourceManager.GetString("Verifying your document, please wait...", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What would you like to do?.
        /// </summary>
        internal static string WhatDoYouWantToDo {
            get {
                return ResourceManager.GetString("WhatDoYouWantToDo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Withdrawal.
        /// </summary>
        internal static string Withdrawal {
            get {
                return ResourceManager.GetString("Withdrawal", resourceCulture);
            }
        }
    }
}
