﻿using System.Windows;

namespace MobileWallet.Desktop
{
    /// <summary>
    /// Interaction logic for ProcessingScreen.xaml
    /// </summary>
    public partial class ProcessingScreen : Window
    {
        private string? _customMessage;

        public ProcessingScreen(string? customMessage = null)
        {
            InitializeComponent();
            _customMessage = customMessage;
            Set_Language();
            this.loaderImage.Source = new Uri(Environment.CurrentDirectory + @"/images/loader1.gif");
        }

        public void Set_Language()
        {
            // If a custom message is provided, use it directly
            if (!string.IsNullOrEmpty(_customMessage))
            {
                TransactionProcessing.Text = _customMessage;
                return;
            }

            // Otherwise, use the default TransactionProcessing resource
            switch (Global.DefaultLanguage)
            {
                case "English":
                    TransactionProcessing.Text = ResourceEnglish.TransactionProcessing;
                    break;
                case "French":
                    TransactionProcessing.Text = ResourceFrench.TransactionProcessing;
                    break;
            }
        }

        public void SetMessage(string message)
        {
            _customMessage = message;
            Set_Language();
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            Set_Language();
            // Start playing the media element
            try
            {
                loaderImage.Play();
            }
            catch (Exception ex)
            {
                App.AppLogger?.Error(ex, "Failed to start media playback in ProcessingScreen");
            }
        }

        private void LoaderImage_OnMediaEnded(object sender, RoutedEventArgs e)
        {
            try
            {
                loaderImage.Position = TimeSpan.Zero;
                loaderImage.Play();
            }
            catch (Exception ex)
            {
                App.AppLogger?.Error(ex, "Failed to restart media playback in ProcessingScreen");
            }
        }
    }
}

