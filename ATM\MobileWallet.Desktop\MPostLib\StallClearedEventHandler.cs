﻿// Decompiled with JetBrains decompiler
// Type: MPOST.StallClearedEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("E2BC7186-5403-4698-B186-5ABB71A8F637")]
  [ComVisible(true)]
  public delegate void StallClearedEventHandler(object sender, EventArgs e);
}
