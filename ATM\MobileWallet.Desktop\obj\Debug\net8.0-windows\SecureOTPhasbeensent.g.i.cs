﻿#pragma checksum "..\..\..\SecureOTPhasbeensent.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C3FE62F4E85268AACD34DC18AC0A9941961067D2"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MobileWallet.Desktop {
    
    
    /// <summary>
    /// SecureOTPhasbeensent
    /// </summary>
    public partial class SecureOTPhasbeensent : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 30 "..\..\..\SecureOTPhasbeensent.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SecureOTPSend;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\SecureOTPhasbeensent.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Submit;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\SecureOTPhasbeensent.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ResendOTPBorder;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\SecureOTPhasbeensent.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResendOTP;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\SecureOTPhasbeensent.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Back;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\SecureOTPhasbeensent.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Cancel;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\SecureOTPhasbeensent.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lblMsgOTP;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\SecureOTPhasbeensent.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtAccountNumber;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\SecureOTPhasbeensent.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid numbperpad;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\SecureOTPhasbeensent.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn1;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\SecureOTPhasbeensent.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn2;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\SecureOTPhasbeensent.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn3;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\SecureOTPhasbeensent.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn4;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\SecureOTPhasbeensent.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn5;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\SecureOTPhasbeensent.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn6;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\SecureOTPhasbeensent.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn7;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\SecureOTPhasbeensent.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn8;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\SecureOTPhasbeensent.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn9;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\SecureOTPhasbeensent.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnclear;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\SecureOTPhasbeensent.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn0;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\SecureOTPhasbeensent.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btndone;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MobileWallet.Desktop;V1.0.0.0;component/secureotphasbeensent.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\SecureOTPhasbeensent.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 8 "..\..\..\SecureOTPhasbeensent.xaml"
            ((MobileWallet.Desktop.SecureOTPhasbeensent)(target)).Loaded += new System.Windows.RoutedEventHandler(this.OnWindowLoaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SecureOTPSend = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.Submit = ((System.Windows.Controls.Button)(target));
            
            #line 43 "..\..\..\SecureOTPhasbeensent.xaml"
            this.Submit.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Submit);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ResendOTPBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 5:
            this.ResendOTP = ((System.Windows.Controls.Button)(target));
            
            #line 56 "..\..\..\SecureOTPhasbeensent.xaml"
            this.ResendOTP.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Resendotp);
            
            #line default
            #line hidden
            return;
            case 6:
            this.Back = ((System.Windows.Controls.Button)(target));
            
            #line 68 "..\..\..\SecureOTPhasbeensent.xaml"
            this.Back.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Back);
            
            #line default
            #line hidden
            return;
            case 7:
            this.Cancel = ((System.Windows.Controls.Button)(target));
            
            #line 79 "..\..\..\SecureOTPhasbeensent.xaml"
            this.Cancel.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Cancel);
            
            #line default
            #line hidden
            return;
            case 8:
            this.lblMsgOTP = ((System.Windows.Controls.Label)(target));
            return;
            case 9:
            this.txtAccountNumber = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.numbperpad = ((System.Windows.Controls.Grid)(target));
            return;
            case 11:
            this.btn1 = ((System.Windows.Controls.Button)(target));
            
            #line 111 "..\..\..\SecureOTPhasbeensent.xaml"
            this.btn1.Click += new System.Windows.RoutedEventHandler(this.btn1_Click_1);
            
            #line default
            #line hidden
            return;
            case 12:
            this.btn2 = ((System.Windows.Controls.Button)(target));
            
            #line 113 "..\..\..\SecureOTPhasbeensent.xaml"
            this.btn2.Click += new System.Windows.RoutedEventHandler(this.btn2_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.btn3 = ((System.Windows.Controls.Button)(target));
            
            #line 115 "..\..\..\SecureOTPhasbeensent.xaml"
            this.btn3.Click += new System.Windows.RoutedEventHandler(this.btn3_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.btn4 = ((System.Windows.Controls.Button)(target));
            
            #line 118 "..\..\..\SecureOTPhasbeensent.xaml"
            this.btn4.Click += new System.Windows.RoutedEventHandler(this.btn4_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.btn5 = ((System.Windows.Controls.Button)(target));
            
            #line 120 "..\..\..\SecureOTPhasbeensent.xaml"
            this.btn5.Click += new System.Windows.RoutedEventHandler(this.btn5_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.btn6 = ((System.Windows.Controls.Button)(target));
            
            #line 122 "..\..\..\SecureOTPhasbeensent.xaml"
            this.btn6.Click += new System.Windows.RoutedEventHandler(this.btn6_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.btn7 = ((System.Windows.Controls.Button)(target));
            
            #line 125 "..\..\..\SecureOTPhasbeensent.xaml"
            this.btn7.Click += new System.Windows.RoutedEventHandler(this.btn7_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.btn8 = ((System.Windows.Controls.Button)(target));
            
            #line 127 "..\..\..\SecureOTPhasbeensent.xaml"
            this.btn8.Click += new System.Windows.RoutedEventHandler(this.btn8_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.btn9 = ((System.Windows.Controls.Button)(target));
            
            #line 129 "..\..\..\SecureOTPhasbeensent.xaml"
            this.btn9.Click += new System.Windows.RoutedEventHandler(this.btn9_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.btnclear = ((System.Windows.Controls.Button)(target));
            
            #line 132 "..\..\..\SecureOTPhasbeensent.xaml"
            this.btnclear.Click += new System.Windows.RoutedEventHandler(this.btnclear_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.btn0 = ((System.Windows.Controls.Button)(target));
            
            #line 141 "..\..\..\SecureOTPhasbeensent.xaml"
            this.btn0.Click += new System.Windows.RoutedEventHandler(this.btn0_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.btndone = ((System.Windows.Controls.Button)(target));
            
            #line 143 "..\..\..\SecureOTPhasbeensent.xaml"
            this.btndone.Click += new System.Windows.RoutedEventHandler(this.Btndone_OnClick);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

