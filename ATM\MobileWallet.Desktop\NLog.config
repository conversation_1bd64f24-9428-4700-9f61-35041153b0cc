﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Warn"
      internalLogFile="internal-nlog.txt"
      throwConfigExceptions="true">

    <targets>
        <!-- Debug & Info file target, rolls daily -->
        <target xsi:type="File"
                name="debugFile"
                fileName="logs/debug-${shortdate}.log"
                layout="${longdate} | ${level:uppercase=true} | ${logger} | ${message} ${exception:format=ToString}"
                archiveEvery="Day"
                archiveNumbering="Date"
                maxArchiveFiles="7"
                keepFileOpen="false"
                encoding="utf-8" />

        <!-- Warn, Error, Fatal log file, rolls daily -->
        <target xsi:type="File"
                name="errorFile"
                fileName="logs/error-${shortdate}.log"
                layout="${longdate} | ${level:uppercase=true} | ${logger} | ${message} ${exception:format=ToString}"
                archiveEvery="Day"
                archiveNumbering="Date"
                maxArchiveFiles="7"
                keepFileOpen="false"
                encoding="utf-8" />

        <!-- Console output for all logs -->
        <target xsi:type="Console"
                name="console"
                layout="${longdate} | ${level:uppercase=true} | ${logger} | ${message} ${exception:format=ToString}" />
    </targets>

    <rules>
        <!-- Debug & Info to debugFile + console -->
        <logger name="*" minlevel="Debug" maxlevel="Info" writeTo="debugFile,console" />

        <!-- Warn and above to errorFile + console -->
        <logger name="*" minlevel="Warn" writeTo="errorFile,console" />
    </rules>
</nlog>