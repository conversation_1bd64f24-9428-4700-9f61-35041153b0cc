﻿// Decompiled with JetBrains decompiler
// Type: MPOST.CalibrateStartEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("3B747E76-7F84-4ef1-A152-6C4A1CACD82C")]
  [ComVisible(true)]
  public delegate void CalibrateStartEventHandler(object sender, EventArgs e);
}
