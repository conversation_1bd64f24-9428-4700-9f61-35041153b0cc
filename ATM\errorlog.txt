2025-07-25 03:09:05.1243 | ERROR | MobileWallet.Desktop.App | The HTTP status code of the response was not expected (401).

Status: 401
Response: 
 HTTP Response: 



MobileWallet.Desktop.API.ApiException: The HTTP status code of the response was not expected (401).

Status: 401
Response: 

   at MobileWallet.Desktop.API.RealTimeClient.RealTime_UpdateAtmRealTimeAsync(UpdateAtmRealTimeRequestModel model, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 7562
   at MobileWallet.Desktop.App.TrackAtmRealTime(UpdateAtmRealTimeRequestModel model) in D:\ATMFinal\MobileWallet.Desktop\App.xaml.cs:line 418
2025-07-25 03:45:15.3939 | ERROR | MobileWallet.Desktop.App | The HTTP status code of the response was not expected (401).

Status: 401
Response: 
 HTTP Response: 



MobileWallet.Desktop.API.ApiException: The HTTP status code of the response was not expected (401).

Status: 401
Response: 

   at MobileWallet.Desktop.API.RealTimeClient.RealTime_UpdateAtmRealTimeAsync(UpdateAtmRealTimeRequestModel model, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 7562
   at MobileWallet.Desktop.App.TrackAtmRealTime(UpdateAtmRealTimeRequestModel model) in D:\ATMFinal\MobileWallet.Desktop\App.xaml.cs:line 418
2025-07-25 03:53:06.2704 | ERROR | MobileWallet.Desktop.App | Cannot control media unless LoadedBehavior or UnloadedBehavior is set to Manual. System.NotSupportedException: Cannot control media unless LoadedBehavior or UnloadedBehavior is set to Manual.
   at System.Windows.Controls.AVElementHelper.SetState(MediaState mediaState)
   at MobileWallet.Desktop.ProcessingScreen.LoaderImage_OnMediaEnded(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\ProcessingScreen.xaml.cs:line 38
   at System.Windows.EventRoute.InvokeHandlersImpl(Object source, RoutedEventArgs args, Boolean reRaised)
   at System.Windows.UIElement.RaiseEventImpl(DependencyObject sender, RoutedEventArgs args)
   at System.Windows.Media.UniqueEventHelper.InvokeEvents(Object sender, EventArgs args)
   at System.Windows.Media.MediaEventsHelper.OnMediaEnded(Object o)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
2025-07-25 03:53:27.3642 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.AppLogClient.AppLog_CreateAppLogAsync(CreateAppLogRequestModel model, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 360
   at MobileWallet.Desktop.App.LogError(String message, LogType logType, String error) in D:\ATMFinal\MobileWallet.Desktop\App.xaml.cs:line 230
2025-07-25 03:56:29.2182 | ERROR | MobileWallet.Desktop.App | The HTTP status code of the response was not expected (401).

Status: 401
Response: 
 HTTP Response: 



MobileWallet.Desktop.API.ApiException: The HTTP status code of the response was not expected (401).

Status: 401
Response: 

   at MobileWallet.Desktop.API.RealTimeClient.RealTime_UpdateAtmRealTimeAsync(UpdateAtmRealTimeRequestModel model, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 7562
   at MobileWallet.Desktop.App.TrackAtmRealTime(UpdateAtmRealTimeRequestModel model) in D:\ATMFinal\MobileWallet.Desktop\App.xaml.cs:line 418
2025-07-25 03:58:34.2957 | ERROR | MobileWallet.Desktop.App | Cannot control media unless LoadedBehavior or UnloadedBehavior is set to Manual. System.NotSupportedException: Cannot control media unless LoadedBehavior or UnloadedBehavior is set to Manual.
   at System.Windows.Controls.AVElementHelper.SetState(MediaState mediaState)
   at MobileWallet.Desktop.ProcessingScreen.LoaderImage_OnMediaEnded(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\ProcessingScreen.xaml.cs:line 38
   at System.Windows.EventRoute.InvokeHandlersImpl(Object source, RoutedEventArgs args, Boolean reRaised)
   at System.Windows.UIElement.RaiseEventImpl(DependencyObject sender, RoutedEventArgs args)
   at System.Windows.Media.UniqueEventHelper.InvokeEvents(Object sender, EventArgs args)
   at System.Windows.Media.MediaEventsHelper.OnMediaEnded(Object o)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
2025-07-25 03:58:34.9064 | ERROR | MobileWallet.Desktop.App | The HTTP status code of the response was not expected (400).

Status: 400
Response: 
{"success":false,"message":"Invalid Object","errors":["Title cannot have more than 50 characters"]} HTTP Response: 

{"success":false,"message":"Invalid Object","errors":["Title cannot have more than 50 characters"]}

MobileWallet.Desktop.API.ApiException: The HTTP status code of the response was not expected (400).

Status: 400
Response: 
{"success":false,"message":"Invalid Object","errors":["Title cannot have more than 50 characters"]}
   at MobileWallet.Desktop.API.AppLogClient.AppLog_CreateAppLogAsync(CreateAppLogRequestModel model, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 388
   at MobileWallet.Desktop.App.LogError(String message, LogType logType, String error) in D:\ATMFinal\MobileWallet.Desktop\App.xaml.cs:line 230
2025-07-25 04:05:37.9573 | ERROR | MobileWallet.Desktop.App | The HTTP status code of the response was not expected (401).

Status: 401
Response: 
 HTTP Response: 



MobileWallet.Desktop.API.ApiException: The HTTP status code of the response was not expected (401).

Status: 401
Response: 

   at MobileWallet.Desktop.API.RealTimeClient.RealTime_UpdateAtmRealTimeAsync(UpdateAtmRealTimeRequestModel model, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 7562
   at MobileWallet.Desktop.App.TrackAtmRealTime(UpdateAtmRealTimeRequestModel model) in D:\ATMFinal\MobileWallet.Desktop\App.xaml.cs:line 418
2025-07-25 04:21:36.4792 | ERROR | MobileWallet.Desktop.App | The HTTP status code of the response was not expected (401).

Status: 401
Response: 
 HTTP Response: 



MobileWallet.Desktop.API.ApiException: The HTTP status code of the response was not expected (401).

Status: 401
Response: 

   at MobileWallet.Desktop.API.RealTimeClient.RealTime_UpdateAtmRealTimeAsync(UpdateAtmRealTimeRequestModel model, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 7562
   at MobileWallet.Desktop.App.TrackAtmRealTime(UpdateAtmRealTimeRequestModel model) in D:\ATMFinal\MobileWallet.Desktop\App.xaml.cs:line 418
2025-07-25 04:34:01.1711 | ERROR | MobileWallet.Desktop.App | The HTTP status code of the response was not expected (401).

Status: 401
Response: 
 HTTP Response: 



MobileWallet.Desktop.API.ApiException: The HTTP status code of the response was not expected (401).

Status: 401
Response: 

   at MobileWallet.Desktop.API.RealTimeClient.RealTime_UpdateAtmRealTimeAsync(UpdateAtmRealTimeRequestModel model, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 7562
   at MobileWallet.Desktop.App.TrackAtmRealTime(UpdateAtmRealTimeRequestModel model) in D:\ATMFinal\MobileWallet.Desktop\App.xaml.cs:line 418
2025-07-25 04:54:27.9369 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 05:00:32.5658 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 05:00:56.1488 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 05:28:07.1202 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 05:32:17.8290 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 06:00:00.3186 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 06:23:56.7130 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 06:35:19.1519 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 06:36:42.0666 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 06:37:10.0448 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 06:37:36.6053 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 06:38:00.7460 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 06:38:25.1506 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 07:11:28.1693 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 07:16:46.6396 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 07:17:49.7939 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 07:18:42.4196 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 07:20:29.0785 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 07:21:02.3660 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 08:00:05.5592 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 08:00:59.2206 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 08:14:18.1933 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 08:14:42.1617 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 08:15:47.9196 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 08:19:48.7970 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 08:20:11.9738 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 08:22:23.0775 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SmsClient.Sms_VerifyOtpAsync(VerifyOtpRequestModel model, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 9024
   at MobileWallet.Desktop.SecureOTPhasbeensent.Button_Click_Submit(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\SecureOTPhasbeensent.xaml.cs:line 304
2025-07-25 08:44:19.4742 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 08:45:24.6187 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 08:46:01.0672 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 08:48:00.0419 | ERROR | MobileWallet.Desktop.App | The HTTP status code of the response was not expected (401).

Status: 401
Response: 
 HTTP Response: 



MobileWallet.Desktop.API.ApiException: The HTTP status code of the response was not expected (401).

Status: 401
Response: 

   at MobileWallet.Desktop.API.RealTimeClient.RealTime_UpdateAtmRealTimeAsync(UpdateAtmRealTimeRequestModel model, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 7562
   at MobileWallet.Desktop.App.TrackAtmRealTime(UpdateAtmRealTimeRequestModel model) in D:\ATMFinal\MobileWallet.Desktop\App.xaml.cs:line 418
2025-07-25 08:55:19.9689 | ERROR | MobileWallet.Desktop.App | The HTTP status code of the response was not expected (400).

Status: 400
Response: 
{"success":false,"message":"Unable to create transaction"} HTTP Response: 

{"success":false,"message":"Unable to create transaction"}

MobileWallet.Desktop.API.ApiException: The HTTP status code of the response was not expected (400).

Status: 400
Response: 
{"success":false,"message":"Unable to create transaction"}
   at MobileWallet.Desktop.API.MtnClient.Mtn_CreateWithdrawRequestAsync(CreateWithdrawRequestRequestModel model, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 6863
   at MobileWallet.Desktop.PleaseEnterAmount.HandleMtnFlow(Int32 amount, String accountNumber, List`1 cassetteDtos) in D:\ATMFinal\MobileWallet.Desktop\PleaseEnterAmount.xaml.cs:line 281
   at MobileWallet.Desktop.PleaseEnterAmount.Button_Click_Submit(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\PleaseEnterAmount.xaml.cs:line 488
2025-07-25 08:56:53.4149 | ERROR | MobileWallet.Desktop.App | The HTTP status code of the response was not expected (400).

Status: 400
Response: 
{"success":false,"message":"Unable to create transaction"} HTTP Response: 

{"success":false,"message":"Unable to create transaction"}

MobileWallet.Desktop.API.ApiException: The HTTP status code of the response was not expected (400).

Status: 400
Response: 
{"success":false,"message":"Unable to create transaction"}
   at MobileWallet.Desktop.API.MtnClient.Mtn_CreateWithdrawRequestAsync(CreateWithdrawRequestRequestModel model, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 6863
   at MobileWallet.Desktop.PleaseEnterAmount.HandleMtnFlow(Int32 amount, String accountNumber, List`1 cassetteDtos) in D:\ATMFinal\MobileWallet.Desktop\PleaseEnterAmount.xaml.cs:line 281
   at MobileWallet.Desktop.PleaseEnterAmount.Button_Click_Submit(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\PleaseEnterAmount.xaml.cs:line 488
2025-07-25 10:30:58.2317 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 10:59:58.9279 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 11:12:32.3301 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 11:26:53.9774 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 11:27:20.3962 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
2025-07-25 11:29:26.2198 | ERROR | MobileWallet.Desktop.App | A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443) System.Net.Http.HttpRequestException: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. (api.wallet2cash.com:443)
 ---> System.Net.Sockets.SocketException (10060): A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at MobileWallet.Desktop.Client.AuthenticatedHttpClientHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\Client\SingletonCLient.cs:line 207
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at MobileWallet.Desktop.API.SessionClient.Session_CreateSessionAsync(CancellationToken cancellationToken) in D:\ATMFinal\MobileWallet.Desktop\API\API.cs:line 8405
   at MobileWallet.Desktop.WelcomeToMobileWallet.Button_Click(Object sender, RoutedEventArgs e) in D:\ATMFinal\MobileWallet.Desktop\WelcomeToMobileWallet.xaml.cs:line 53
