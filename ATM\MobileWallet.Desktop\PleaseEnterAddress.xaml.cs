﻿using System.ComponentModel;
using System.Drawing;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Imaging;
using System.Windows.Threading;
using Emgu.CV;
using Emgu.CV.CvEnum;
using MobileWallet.Desktop.API;
using MobileWallet.Desktop.Client;
using MobileWallet.Desktop.Extensions;
using MobileWallet.Desktop.Helpers;
using QRCoder;

namespace MobileWallet.Desktop;

public partial class PleaseEnterAddress : Window
{
    private VideoCapture? _capture;
    private DispatcherTimer? _timer;
    private bool _isRetake = false;

    public PleaseEnterAddress()
    {
        InitializeComponent();
        if (Global.IsTest)
        {
            TxtAddress.Text = "Aqtv4NGiHwakyAhJjm6QTrtyhyr7JXjoXDvhWK8wkJe2";
        }

        SetLanguage();
    }

    private void SetLanguage()
    {
        if (Global.IsFrench)
        {
            BtnBack.Content = ResourceFrench.Back;
            BtnCancel.Content = ResourceFrench.Cancel;
            BtnSubmit.Content = ResourceFrench.Submit;
            WindowTitle.Text = ResourceFrench.PlaceYourQrCodeAddress;
        }
        else
        {
            BtnBack.Content = ResourceEnglish.Back;
            BtnCancel.Content = ResourceEnglish.Cancel;
            BtnSubmit.Content = ResourceEnglish.Submit;
            WindowTitle.Text = ResourceEnglish.PlaceYourQrCodeAddress;
        }
    }

    // private void Button_Click_Capture(object sender, RoutedEventArgs e)
    // {
    //     if (_isRetake)
    //     {
    //         if (Global.UseCamera)
    //         {
    //             StartCamera();
    //         }
    //         _isRetake = false;
    //         this.SubmitBorder.Visibility = Visibility.Hidden;
    //         if (Global.IsFrench)
    //         {
    //             this.BtnCapture.Content = ResourceFrench.Capture;
    //         }
    //         else
    //         {
    //             this.BtnCapture.Content = ResourceEnglish.Capture;
    //         }
    //     }
    //     else
    //     {
    //         if (Global.UseCamera)
    //         {
    //             StopCamera();
    //         }
    //         this.SubmitBorder.Visibility = Visibility.Visible;
    //         if (Global.IsFrench)
    //         {
    //             this.BtnCapture.Content = ResourceFrench.RetakeImage;
    //         }
    //         else
    //         {
    //             this.BtnCapture.Content = ResourceEnglish.RetakeImage;
    //         }
    //         _isRetake = true;
    //     }
    // }

    private void Button_Click_Cancel(object sender, RoutedEventArgs e)
    {
        WelcomeToMobileWallet note = new WelcomeToMobileWallet();
        note.Show();
        this.Close();
    }

    private void Button_Click(object sender, RoutedEventArgs e)
    {
        SelectCryptoQuotation note = new SelectCryptoQuotation();
        note.Show();
        this.Close();
    }

    private void StartCamera()
    {
        try
        {
            if (_capture == null)
            {
                _capture = new VideoCapture(Global.CameraIndex, VideoCapture.API.DShow);
                _capture.Set(CapProp.Fps, 30); // Optional: Set FPS
                _capture.Set(CapProp.FrameWidth, 640); // Optional: Set Width
                _capture.Set(CapProp.FrameHeight, 480); // Optional: Set Height
            }

            if (_timer == null)
            {
                _timer = new DispatcherTimer
                {
                    Interval = TimeSpan.FromMilliseconds(
                        33
                    ) // ~30 FPS
                    ,
                };
                _timer.Tick += UpdateFrame;
            }
            _timer.Start();
        }
        catch (Exception ex)
        {
            App.AppLogger.Error(ex, ex.Message);
            MessageBox.Show($"Error initializing camera: {ex.Message}");
        }
    }

    private void UpdateFrame(object? sender, EventArgs e)
    {
        if (_capture != null && _capture.IsOpened)
        {
            using (Mat frame = _capture.QueryFrame())
            {
                if (frame != null && !frame.IsEmpty)
                {
                    CameraImage.Source = frame.ToBitmapSource();
                    TxtAddress.Text = frame.ToQrCode() ?? TxtAddress.Text;
                }
            }
        }
    }

    private void StopCamera()
    {
        _timer?.Stop();
        _capture?.Dispose();
        _timer = null;
        _capture = null;
    }

    private void PleaseEnterAddress_OnLoaded(object sender, RoutedEventArgs e)
    {
        Helper.AdjustRowHeight(this, DynamicRow, 300);
        App.StartTimer(this);
        _ = App.TrackAtmRealTime(
            new UpdateAtmRealTimeRequestModel() { CurrentScreen = nameof(PleaseEnterAddress) }
        );
        TxtAddress.Focus();
        if (Global.UseCamera)
        {
            // StartCamera();
        }
    }

    private void PleaseEnterAddress_OnClosing(object? sender, CancelEventArgs e)
    {
        App.StopTimer();
        // StopCamera();
    }

    private async void Button_Click_Submit(object sender, RoutedEventArgs e)
    {
        try
        {
            ButtonHelper.ToggleButton(sender);
            App.ShowProcessingDialog();
            Global.UserAddress = QrLabel.Text;
            if (Global.UserAddress.Length > 5)
            {
                var result = await new API.CryptoClient(
                    HttpClientSingleton.Instance
                ).Crypto_VerifyCryptoAddressAsync(
                    new VerifyCryptoAddressRequestModel()
                    {
                        Address = Global.UserAddress,
                        Chain = Global.SelectedToken.Chain,
                    }
                );
                ButtonHelper.ToggleButton(sender);
                if (result.Data)
                {
                    if (Global.UseV2)
                    {
                        PleaseInsertNoteV2 insertNote = new PleaseInsertNoteV2();
                        insertNote.Show();
                    }
                    else
                    {
                        PleaseInsertNote insertNote = new PleaseInsertNote();
                        insertNote.Show();
                    }
                    Close();
                }
                else
                {
                    App.HideProcessingDialog();
                    CustomMessageBox.Show("Invalid Address");
                }
            }
            else
            {
                ButtonHelper.ToggleButton(sender);
                App.HideProcessingDialog();
                CustomMessageBox.Show("Invalid Address");
            }
        }
        catch (Exception exception)
        {
            App.AppLogger.Error(exception, exception.Message);
            ButtonHelper.ToggleButton(sender);
            App.HideProcessingDialog();
            CustomMessageBox.Show("Invalid Address");
        }
        finally
        {
            App.HideProcessingDialog();
        }
    }

    private void GotTextFocus(object sender, RoutedEventArgs e)
    {
        App.OpenKeyBoard();
    }

    private void OnLostTextFocus(object sender, RoutedEventArgs e)
    {
        App.CloseKeyBoard();
    }

    private DispatcherTimer? debounceTimer;

    private void TxtAddress_OnTextChanged(object sender, TextChangedEventArgs e)
    {
        if (debounceTimer == null)
        {
            debounceTimer = new DispatcherTimer();
            debounceTimer.Interval = TimeSpan.FromMilliseconds(500); // Debounce time
            debounceTimer.Tick += DebounceTimer_Tick;
        }
        debounceTimer.Stop(); // reset timer
        debounceTimer.Start();
    }

    private void DebounceTimer_Tick(object? sender, EventArgs e)
    {
        debounceTimer?.Stop();
        var text = TxtAddress.Text.Split(':').Last();
        GenerateQrCode(text);
        if (!string.IsNullOrWhiteSpace(text))
        {
            QrLabel.Text = text;
        }
        TxtAddress.Text = "";
    }

    private void GenerateQrCode(string text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return;

        using (QRCodeGenerator qrGenerator = new QRCodeGenerator())
        {
            QRCodeData qrCodeData = qrGenerator.CreateQrCode(text, QRCodeGenerator.ECCLevel.Q);
            using (QRCode qrCode = new QRCode(qrCodeData))
            {
                Bitmap qrBitmap = qrCode.GetGraphic(20);
                CameraImage.Source = qrBitmap.ToBitmapSource();
            }
        }
    }
}
