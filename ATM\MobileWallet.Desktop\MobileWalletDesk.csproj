﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup Label="Globals">
		<SccProjectName>SAK</SccProjectName>
		<SccProvider>SAK</SccProvider>
		<SccAuxPath>SAK</SccAuxPath>
		<SccLocalPath>SAK</SccLocalPath>
		<Platforms>AnyCPU;x86</Platforms>
	</PropertyGroup>
	<PropertyGroup>
		<TargetPlatformVersion>10</TargetPlatformVersion>
		<OutputType>WinExe</OutputType>
		<TargetFramework>net8.0-windows</TargetFramework>
		<UseWPF>true</UseWPF>
		<PlatformTarget>AnyCPU</PlatformTarget>
		<AssemblyName>$(MSBuildProjectName)</AssemblyName>
		<AllowUnsafeBlocks>true</AllowUnsafeBlocks>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="Context\Models\**" />
	  <Compile Remove="NewFolder\**" />
	  <Content Remove="Context\Models\**" />
	  <Content Remove="NewFolder\**" />
	  <EmbeddedResource Remove="Context\Models\**" />
	  <EmbeddedResource Remove="NewFolder\**" />
	  <None Remove="Context\Models\**" />
	  <None Remove="NewFolder\**" />
	  <Page Remove="Context\Models\**" />
	  <Page Remove="NewFolder\**" />
	  <PRIResource Remove="Context\Models\**" />
	  <PRIResource Remove="NewFolder\**" />
	</ItemGroup>

	<ItemGroup>
	  <Content Remove="images\back-drop1.png" />
	  <Content Remove="images\Back.jpg" />
	  <Content Remove="images\Cash_Acceptor.jpg" />
	  <Content Remove="images\mtn1.png" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="IronOcr" Version="2024.8.21" />
		<PackageReference Include="log4net" Version="2.0.17" />
		<PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
		<PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="5.2.7" />
		<PackageReference Include="Microsoft.Data.SqlClient" Version="3.1.0" />
		<PackageReference Include="Microsoft.ProjectReunion" Version="0.5.9" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="3.1.32" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="3.1.32">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="3.1.32" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="3.1.32">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="SerialPortStream" Version="2.4.1" />
		<PackageReference Include="System.IO.Ports" Version="4.7.0" />
		<PackageReference Include="System.Resources.ResourceManager" Version="4.3.0" />
		<PackageReference Include="System.Runtime.InteropServices" Version="4.3.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\ATM-Linkitsoft-Camera\ATM-Linkitsoft-Camera\Camera Updated\Camera Updated.csproj" />
		<ProjectReference Include="..\ATM-Linkitsoft-CashDispenser\ATM-Linkitsoft-CashDispenser\CashDispenser\CashDispenser.csproj" />
		<ProjectReference Include="..\ATM-Linkitsoft-Passport\ATM-Linkitsoft-Passport\WPFPassport\WPFPassport.csproj" />
		<ProjectReference Include="..\MobileWallet.DataAccess\MobileWallet.DataAccess.csproj" />
		<ProjectReference Include="..\MobileWallet.Models\MobileWallet.Models.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Reference Include="AxInterop.ActiveXLib">
	    <HintPath>..\ATM-Linkitsoft\testing app 1\bin\Debug\AxInterop.ActiveXLib.dll</HintPath>
	  </Reference>
	  <Reference Include="MPOST">
	    <HintPath>..\ATM-Linkitsoft-CashAcceptor\ATM-Linkitsoft-CashAcceptor\MPOST.dll</HintPath>
	  </Reference>
	</ItemGroup>

	<ItemGroup>
		<Compile Update="ResourceEnglish.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>ResourceEnglish.resx</DependentUpon>
		</Compile>
		<Compile Update="ResourceFrench.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>ResourceFrench.resx</DependentUpon>
		</Compile>
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Update="ResourceEnglish.resx">
			<Generator>ResXFileCodeGenerator</Generator>
			<LastGenOutput>ResourceEnglish.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="ResourceFrench.resx">
			<Generator>ResXFileCodeGenerator</Generator>
			<LastGenOutput>ResourceFrench.Designer.cs</LastGenOutput>
		</EmbeddedResource>
	</ItemGroup>

	<ItemGroup>
		<None Include="images\back-drop.png">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="images\back-drop1.png">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="images\Cash_Acceptor.jpg">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="images\cut.png">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="images\loader.gif">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="images\loader1.gif">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="images\logo.png">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="images\logo1.png">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="images\mtn.png">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="images\mtn1.png">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="images\orange.png">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="images\scan.png">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
		<Page Update="Window10.xaml">
			<XamlRuntime>$(DefaultXamlRuntime)</XamlRuntime>
			<SubType>Designer</SubType>
		</Page>
		<Page Update="SelectCashOut.xaml">
			<XamlRuntime>$(DefaultXamlRuntime)</XamlRuntime>
		</Page>
		<Page Update="PleaseEnterAmount.xaml">
			<XamlRuntime>$(DefaultXamlRuntime)</XamlRuntime>
		</Page>
		<Page Update="SecureOTPhasbeensent.xaml">
			<XamlRuntime>$(DefaultXamlRuntime)</XamlRuntime>
			<SubType>Designer</SubType>
		</Page>
		<Page Update="EnterAccountNumber.xaml">
			<XamlRuntime>$(DefaultXamlRuntime)</XamlRuntime>
		</Page>
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Capture\" />
	  <Folder Include="PassPort\" />
	</ItemGroup>

	<ItemGroup>
	  <None Include="images\Back.jpg">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	</ItemGroup>

	<ItemGroup>
	  <None Update="MPOST.dll">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	  <None Update="Msprintsdk.dll">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	  <None Update="SampleVedio.mp4">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	</ItemGroup>

</Project>
