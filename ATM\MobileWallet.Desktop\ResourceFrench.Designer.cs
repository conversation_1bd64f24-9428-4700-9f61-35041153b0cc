﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace MobileWallet.Desktop {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class ResourceFrench {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResourceFrench() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("MobileWallet.Desktop.ResourceFrench", typeof(ResourceFrench).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Montant.
        /// </summary>
        internal static string Amount {
            get {
                return ResourceManager.GetString("Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Retour.
        /// </summary>
        internal static string Back {
            get {
                return ResourceManager.GetString("Back", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Effacer Tout.
        /// </summary>
        internal static string btnclear {
            get {
                return ResourceManager.GetString("btnclear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Effacer.
        /// </summary>
        internal static string btndone {
            get {
                return ResourceManager.GetString("btndone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Suivante.
        /// </summary>
        internal static string btnnext {
            get {
                return ResourceManager.GetString("btnnext", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Acheter.
        /// </summary>
        internal static string Buy {
            get {
                return ResourceManager.GetString("Buy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Achat/vente au-dessus de 10 000 XAF.
        /// </summary>
        internal static string BuyAboveLimit {
            get {
                return ResourceManager.GetString("BuyAboveLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Achat/vente en dessous de 10 000 XAF.
        /// </summary>
        internal static string BuyBelowLimit {
            get {
                return ResourceManager.GetString("BuyBelowLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Annuler.
        /// </summary>
        internal static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Capturer.
        /// </summary>
        internal static string Capture {
            get {
                return ResourceManager.GetString("Capture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirmer le numéro de compte.
        /// </summary>
        internal static string Confirm_Account_Number {
            get {
                return ResourceManager.GetString("Confirm Account Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Crypto.
        /// </summary>
        internal static string Crypto {
            get {
                return ResourceManager.GetString("Crypto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dépôt.
        /// </summary>
        internal static string Deposit {
            get {
                return ResourceManager.GetString("Deposit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Terminé.
        /// </summary>
        internal static string Done {
            get {
                return ResourceManager.GetString("Done", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Permis de conduire.
        /// </summary>
        internal static string Driving_License {
            get {
                return ResourceManager.GetString("Driving License", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Anglais.
        /// </summary>
        internal static string English {
            get {
                return ResourceManager.GetString("English", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Entrez le numéro de compte.
        /// </summary>
        internal static string EnterAccountNo {
            get {
                return ResourceManager.GetString("EnterAccountNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Veuillez entrer le montant (entre 5000Fcfa et 200,000Fcfa).
        /// </summary>
        internal static string EnterAmount {
            get {
                return ResourceManager.GetString("EnterAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Enter Amount (between $5 and $5,000).
        /// </summary>
        internal static string EnterAmountUsd {
            get {
                return ResourceManager.GetString("EnterAmountUsd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prix moyen.
        /// </summary>
        internal static string EstAveragePrice {
            get {
                return ResourceManager.GetString("EstAveragePrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Frais.
        /// </summary>
        internal static string EstFee {
            get {
                return ResourceManager.GetString("EstFee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Glissement.
        /// </summary>
        internal static string EstSlippage {
            get {
                return ResourceManager.GetString("EstSlippage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exchange Rate.
        /// </summary>
        internal static string ExchangeRate {
            get {
                return ResourceManager.GetString("ExchangeRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to français.
        /// </summary>
        internal static string French {
            get {
                return ResourceManager.GetString("French", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Combien voulez-vous acheter/vendre ?.
        /// </summary>
        internal static string HowMuchDoYouWantToBuy {
            get {
                return ResourceManager.GetString("HowMuchDoYouWantToBuy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Carte d&apos;identité.
        /// </summary>
        internal static string IDCard {
            get {
                return ResourceManager.GetString("IDCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Veuillez entrer le numéro de compte.
        /// </summary>
        internal static string lblMsg {
            get {
                return ResourceManager.GetString("lblMsg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Veuillez entrer le montant.
        /// </summary>
        internal static string lblMsgEnterAmount {
            get {
                return ResourceManager.GetString("lblMsgEnterAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Veuillez entrer le code OTP reçu :.
        /// </summary>
        internal static string lblMsgOTP {
            get {
                return ResourceManager.GetString("lblMsgOTP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Marché.
        /// </summary>
        internal static string Market {
            get {
                return ResourceManager.GetString("Market", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Momo.
        /// </summary>
        internal static string MtnMoney {
            get {
                return ResourceManager.GetString("MtnMoney", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Orange.
        /// </summary>
        internal static string OrangeMoney {
            get {
                return ResourceManager.GetString("OrangeMoney", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Les autres.
        /// </summary>
        internal static string Others {
            get {
                return ResourceManager.GetString("Others", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Le distributeur automatique est hors service.
        /// </summary>
        internal static string OutOfService {
            get {
                return ResourceManager.GetString("OutOfService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Old Id Card/Passeport.
        /// </summary>
        internal static string Passport {
            get {
                return ResourceManager.GetString("Passport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Méthode de paiement.
        /// </summary>
        internal static string PaymentMethod {
            get {
                return ResourceManager.GetString("PaymentMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Placez votre carte alignée avec l&apos;appareil photo.
        /// </summary>
        internal static string Place_Your_Card_Align_With_The_Camera {
            get {
                return ResourceManager.GetString("Place Your Card Align With The Camera", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Placez votre carte d&apos;identité devant la caméra.
        /// </summary>
        internal static string PlaceIdCardInsideCamera {
            get {
                return ResourceManager.GetString("PlaceIdCardInsideCamera", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Placez votre passeport à l&apos;intérieur du scanner.
        /// </summary>
        internal static string PlacePassportInsideCamera {
            get {
                return ResourceManager.GetString("PlacePassportInsideCamera", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please scan your wallet address below using the QR Code Scanner.
        /// </summary>
        internal static string PlaceYourQrCodeAddress {
            get {
                return ResourceManager.GetString("PlaceYourQrCodeAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please scan your transaction Id below using the QR Code Scanner.
        /// </summary>
        internal static string PlaceYourQrCodeAlignWithTheCamera {
            get {
                return ResourceManager.GetString("PlaceYourQrCodeAlignWithTheCamera", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Veuillez entrer le numéro de compte.
        /// </summary>
        internal static string Please_Enter_Account_Number {
            get {
                return ResourceManager.GetString("Please Enter Account Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter amount you want to deposit.
        /// </summary>
        internal static string PleaseEnterAmountDeposit {
            get {
                return ResourceManager.GetString("PleaseEnterAmountDeposit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter amount you want to withdraw.
        /// </summary>
        internal static string PleaseEnterAmountWithdraw {
            get {
                return ResourceManager.GetString("PleaseEnterAmountWithdraw", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to you will receive.
        /// </summary>
        internal static string QuoteDeposit {
            get {
                return ResourceManager.GetString("QuoteDeposit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to you will sell.
        /// </summary>
        internal static string QuoteWithdraw {
            get {
                return ResourceManager.GetString("QuoteWithdraw", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Renvoyer le code.
        /// </summary>
        internal static string ResendOTP {
            get {
                return ResourceManager.GetString("ResendOTP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CV.
        /// </summary>
        internal static string Resume {
            get {
                return ResourceManager.GetString("Resume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reprendre l&apos;image.
        /// </summary>
        internal static string RetakeImage {
            get {
                return ResourceManager.GetString("RetakeImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Secure OTP a été envoyé à votre registre no..
        /// </summary>
        internal static string SecureOTPSend {
            get {
                return ResourceManager.GetString("SecureOTPSend", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Un code sécurisé a été envoyé sur le compte {Phone}\nNom : {Name}.
        /// </summary>
        internal static string SecureOTPSendWithName {
            get {
                return ResourceManager.GetString("SecureOTPSendWithName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Un code sécurisé a été envoyé sur le compte {Phone}.
        /// </summary>
        internal static string SecureOTPSendWithPhone {
            get {
                return ResourceManager.GetString("SecureOTPSendWithPhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sélectionnez le montant.
        /// </summary>
        internal static string Select_Amount {
            get {
                return ResourceManager.GetString("Select Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sélectionnez Encaissement.
        /// </summary>
        internal static string Select_Cash_Out {
            get {
                return ResourceManager.GetString("Select Cash Out", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sélectionnez votre identifiant et insérez-le dans la zone du scanner.
        /// </summary>
        internal static string Select_Your_ID___Insert_To_The_Scanner_Area {
            get {
                return ResourceManager.GetString("Select Your ID & Insert To The Scanner Area", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sélectionnez votre identifiant et insérez-le dans la zone du scanner.
        /// </summary>
        internal static string Select_Your_ID_and_Insert_To_The_Scanner_Area {
            get {
                return ResourceManager.GetString("Select Your ID and Insert To The Scanner Area", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sélectionnez Dépôt ou Retrait.
        /// </summary>
        internal static string SelectCashInOut {
            get {
                return ResourceManager.GetString("SelectCashInOut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sélectionnez le réseau crypto.
        /// </summary>
        internal static string SelectCryptoNetwork {
            get {
                return ResourceManager.GetString("SelectCryptoNetwork", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sélectionnez le jeton crypto.
        /// </summary>
        internal static string SelectCryptoToken {
            get {
                return ResourceManager.GetString("SelectCryptoToken", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sélectionner la méthode d&apos;identification.
        /// </summary>
        internal static string SelectIdentificationMethod {
            get {
                return ResourceManager.GetString("SelectIdentificationMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selectionnez la langue.
        /// </summary>
        internal static string SelectLaunguage {
            get {
                return ResourceManager.GetString("SelectLaunguage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sélectionnez l&apos;opérateur.
        /// </summary>
        internal static string SelectMoneyOperator {
            get {
                return ResourceManager.GetString("SelectMoneyOperator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendre.
        /// </summary>
        internal static string Sell {
            get {
                return ResourceManager.GetString("Sell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell above 10,000 XAF.
        /// </summary>
        internal static string SellAboveLimit {
            get {
                return ResourceManager.GetString("SellAboveLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell below 10,000 XAF.
        /// </summary>
        internal static string SellBelowLimit {
            get {
                return ResourceManager.GetString("SellBelowLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Valider.
        /// </summary>
        internal static string Submit {
            get {
                return ResourceManager.GetString("Submit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Merci . . ..
        /// </summary>
        internal static string Thanks {
            get {
                return ResourceManager.GetString("Thanks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Local.
        /// </summary>
        internal static string TotalLocal {
            get {
                return ResourceManager.GetString("TotalLocal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total USD.
        /// </summary>
        internal static string TotalUsd {
            get {
                return ResourceManager.GetString("TotalUsd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Traitement de la transaction....
        /// </summary>
        internal static string TransactionProcessing {
            get {
                return ResourceManager.GetString("TransactionProcessing", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Authentification....
        /// </summary>
        internal static string Authenticating {
            get {
                return ResourceManager.GetString("Authenticating", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Veuillez patienter....
        /// </summary>
        internal static string PleaseWait {
            get {
                return ResourceManager.GetString("PleaseWait", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Vérification OTP....
        /// </summary>
        internal static string VerifyingOTP {
            get {
                return ResourceManager.GetString("VerifyingOTP", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Chargement des jetons....
        /// </summary>
        internal static string LoadingTokens {
            get {
                return ResourceManager.GetString("LoadingTokens", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Récupération des taux....
        /// </summary>
        internal static string FetchingRates {
            get {
                return ResourceManager.GetString("FetchingRates", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Chargement des cotations....
        /// </summary>
        internal static string LoadingQuotations {
            get {
                return ResourceManager.GetString("LoadingQuotations", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Création de session....
        /// </summary>
        internal static string CreatingSession {
            get {
                return ResourceManager.GetString("CreatingSession", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Vérification de l'adresse....
        /// </summary>
        internal static string VerifyingAddress {
            get {
                return ResourceManager.GetString("VerifyingAddress", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Traitement de la demande....
        /// </summary>
        internal static string ProcessingRequest {
            get {
                return ResourceManager.GetString("ProcessingRequest", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Chargement des données....
        /// </summary>
        internal static string LoadingData {
            get {
                return ResourceManager.GetString("LoadingData", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Vérifiez les taux crypto.
        /// </summary>
        internal static string VerifyCryptoRates {
            get {
                return ResourceManager.GetString("VerifyCryptoRates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Que voulez-vous faire ?.
        /// </summary>
        internal static string WhatDoYouWantToDo {
            get {
                return ResourceManager.GetString("WhatDoYouWantToDo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Retrait.
        /// </summary>
        internal static string Withdrawal {
            get {
                return ResourceManager.GetString("Withdrawal", resourceCulture);
            }
        }
    }
}
