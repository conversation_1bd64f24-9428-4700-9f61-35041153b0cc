﻿using MobileWallet.Desktop.API;
using MobileWallet.Desktop.Atm;

namespace MobileWallet.Desktop;

public class Global
{
    // public static int CameraIndex = 0;

    // public const string BaseUrl = "https://localhost:5001/";

    public const int CameraIndex = 1;
    public const string BaseUrl = "https://api.wallet2cash.com/";
    public const bool UseHardware = true;
    public const bool UseCamera = false;
    public const bool UseOtp = false;
    public const bool IsTest = true;
    public const string Currency = "USD";
    public const bool UseV2 = false;
    public static bool IsDeposit = false;
    public static bool UsePrinter = false;
    public static TimeSpan CryptoWithdrawWaitTime = TimeSpan.FromMinutes(5);
    public static string DefaultLanguage { get; set; } = "English";
    public static string CurrentAccountNumber = "";
    public static string Username { get; set; } = "";
    public static bool IsFrench => DefaultLanguage == "French";
    public static bool IsCrypto { get; set; } = false;
    public static bool IsCryptoAbove { get; set; } = false;
    public static CryptoNetworkDto SelectedNetwork { get; set; } = null;
    public static CryptoTokenDto SelectedToken { get; set; } = null;
    public static string UserAddress { get; set; } = "";
    public static PassportReaderV2.PassportInfo? SelectedPassPort { get; set; }
    public static PassportReaderV3.DocData? SelectedPassPortV2 { get; set; }
    public static AtmProfileDto Profile { get; set; } = null;
}
