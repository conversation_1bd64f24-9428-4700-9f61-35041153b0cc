﻿#pragma checksum "..\..\..\SelectCryptoRange.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "63CFAF135F51D5ADEFECFE88431A25E59E571426"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MobileWallet.Desktop {
    
    
    /// <summary>
    /// SelectCryptoRange
    /// </summary>
    public partial class SelectCryptoRange : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 20 "..\..\..\SelectCryptoRange.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MyHeader;
        
        #line default
        #line hidden
        
        
        #line 24 "..\..\..\SelectCryptoRange.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnBuyAbove;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\SelectCryptoRange.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnBuyBelow;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\SelectCryptoRange.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Back;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\SelectCryptoRange.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Cancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MobileWalletDesk;component/selectcryptorange.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\SelectCryptoRange.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 7 "..\..\..\SelectCryptoRange.xaml"
            ((MobileWallet.Desktop.SelectCryptoRange)(target)).Closing += new System.ComponentModel.CancelEventHandler(this.SelectCryptoRange_OnClosing);
            
            #line default
            #line hidden
            
            #line 8 "..\..\..\SelectCryptoRange.xaml"
            ((MobileWallet.Desktop.SelectCryptoRange)(target)).Loaded += new System.Windows.RoutedEventHandler(this.SelectCryptoRange_OnLoaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.MyHeader = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.BtnBuyAbove = ((System.Windows.Controls.Button)(target));
            
            #line 26 "..\..\..\SelectCryptoRange.xaml"
            this.BtnBuyAbove.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Above);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BtnBuyBelow = ((System.Windows.Controls.Button)(target));
            
            #line 34 "..\..\..\SelectCryptoRange.xaml"
            this.BtnBuyBelow.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Below);
            
            #line default
            #line hidden
            return;
            case 5:
            this.Back = ((System.Windows.Controls.Button)(target));
            
            #line 42 "..\..\..\SelectCryptoRange.xaml"
            this.Back.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Back);
            
            #line default
            #line hidden
            return;
            case 6:
            this.Cancel = ((System.Windows.Controls.Button)(target));
            
            #line 50 "..\..\..\SelectCryptoRange.xaml"
            this.Cancel.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Cancel);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

