﻿using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using MobileWallet.Desktop.API;
using Newtonsoft.Json;

namespace MobileWallet.Desktop
{
    /// <summary>
    /// Interaction logic for Window2.xaml
    /// </summary>
    public partial class Window6 : Window
    {
        public Window6()
        {
            InitializeComponent();
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            CardAlignWithTheCamera NewWindow = new CardAlignWithTheCamera();
            NewWindow.Show();
            this.Close();
        }

        private void Button_Click_2(object sender, RoutedEventArgs e)
        {
            WelcomeToMobileWallet mainWindow = new WelcomeToMobileWallet();
            mainWindow.Show();
            this.Close();
        }

        private void SetAccountNumber(string value)
        {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.Append(value);
            string finalvalue = stringBuilder.ToString();
            txtAccountNumber.Text += finalvalue;
        }

        private void btn1_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn1.Content.ToString());
        }

        private void btn2_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn2.Content.ToString());
        }

        private void btn3_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn3.Content.ToString());
        }

        private void btn4_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn4.Content.ToString());
        }

        private void btn5_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn5.Content.ToString());
        }

        private void btn6_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn6.Content.ToString());
        }

        private void btn7_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn7.Content.ToString());
        }

        private void btn8_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn8.Content.ToString());
        }

        private void btn9_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn9.Content.ToString());
        }

        private void btn0_Click(object sender, RoutedEventArgs e)
        {
            SetAccountNumber(btn0.Content.ToString());
        }

        private void btnremovenumber_Click(object sender, RoutedEventArgs e)
        {

        }
        private void btnclear_Click(object sender, RoutedEventArgs e)
        {
            txtAccountNumber.Text = string.Empty;
        }

        private void btndone_Click(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(txtAccountNumber.Text))
            {
                int length = txtAccountNumber.Text.Length;
                string restnumbers = txtAccountNumber.Text.Substring(0, length - 1);
                txtAccountNumber.Text = restnumbers;

            }
        }

        private void Button_Click_Submit(object sender, RoutedEventArgs e)
        {
            if (txtAccountNumber.Text.Length > 12 || txtAccountNumber.Text.Length < 12)
            {
                MessageBox.Show("Invalid Mobile Number !!");

                txtAccountNumber.Focus();
            }
            else
            {
                int length = txtAccountNumber.Text.Length;
                HttpClientHandler clientHandler = new HttpClientHandler();
                string jsonData = JsonConvert.SerializeObject(txtAccountNumber.Text.Substring(0, length));
                string mobileNum = Regex.Replace(jsonData, @",(?=[^""]*""(?:[^""]*""[^""]*"")*[^""]*$)", string.Empty);
                // HttpContent content = new StringContent(mobileNum);
                //string apiURL = "https://localhost:44372/api/Sms/SendOtp";
                string apiURL = "https://mobilewallet-api.conveyor.cloud/api/Sms/SendOtp";

                clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };
                using (var client1 = new HttpClient(clientHandler))
                {

                    //Todo Fix this
                    // var userDataModel = new MobileNoModel()
                    // {
                    //     MobileNumber = Convert.ToInt64(txtAccountNumber.Text)
                    // };
                    //
                    // var postTask = client1.PostAsJsonAsync<MobileNoModel>(apiURL, userDataModel);
                    // postTask.Wait();
                    //
                    // var result = postTask.Result;
                    // if (result.IsSuccessStatusCode)
                    // {
                    //     Window7 NewWindow = new Window7();
                    //     NewWindow.Show();
                    //     this.Close();
                    //     MessageBox.Show("Secure otp successfully sent on your registered no.", "Alert", MessageBoxButton.OK, MessageBoxImage.Information);
                    // }
                    // else
                    // {
                    //     Window7 NewWindow = new Window7();
                    //     NewWindow.Show();
                    //     this.Close();
                    //     MessageBox.Show("Something went wrong!.", "Alert", MessageBoxButton.OK, MessageBoxImage.Information);
                    // }
                }
            }
            
        }

    }
}


