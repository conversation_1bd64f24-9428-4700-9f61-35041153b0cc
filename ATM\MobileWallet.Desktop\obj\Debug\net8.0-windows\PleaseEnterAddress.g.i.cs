﻿#pragma checksum "..\..\..\PleaseEnterAddress.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "D1C447BB9AFB3C037CD1100B3AD3FDCB9C9C677C"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MobileWallet.Desktop {
    
    
    /// <summary>
    /// PleaseEnterAddress
    /// </summary>
    public partial class PleaseEnterAddress : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 18 "..\..\..\PleaseEnterAddress.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RowDefinition DynamicRow;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\PleaseEnterAddress.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WindowTitle;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\PleaseEnterAddress.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnBack;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\PleaseEnterAddress.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCancel;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\PleaseEnterAddress.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border SubmitBorder;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\PleaseEnterAddress.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSubmit;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\PleaseEnterAddress.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CameraImage;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\PleaseEnterAddress.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock QrLabel;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\PleaseEnterAddress.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtAddress;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MobileWallet.Desktop;V1.0.0.0;component/pleaseenteraddress.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\PleaseEnterAddress.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 8 "..\..\..\PleaseEnterAddress.xaml"
            ((MobileWallet.Desktop.PleaseEnterAddress)(target)).Closing += new System.ComponentModel.CancelEventHandler(this.PleaseEnterAddress_OnClosing);
            
            #line default
            #line hidden
            
            #line 9 "..\..\..\PleaseEnterAddress.xaml"
            ((MobileWallet.Desktop.PleaseEnterAddress)(target)).Loaded += new System.Windows.RoutedEventHandler(this.PleaseEnterAddress_OnLoaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.DynamicRow = ((System.Windows.Controls.RowDefinition)(target));
            return;
            case 3:
            this.WindowTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.BtnBack = ((System.Windows.Controls.Button)(target));
            
            #line 38 "..\..\..\PleaseEnterAddress.xaml"
            this.BtnBack.Click += new System.Windows.RoutedEventHandler(this.Button_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BtnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 42 "..\..\..\PleaseEnterAddress.xaml"
            this.BtnCancel.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Cancel);
            
            #line default
            #line hidden
            return;
            case 6:
            this.SubmitBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 7:
            this.BtnSubmit = ((System.Windows.Controls.Button)(target));
            
            #line 48 "..\..\..\PleaseEnterAddress.xaml"
            this.BtnSubmit.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Submit);
            
            #line default
            #line hidden
            return;
            case 8:
            this.CameraImage = ((System.Windows.Controls.Image)(target));
            return;
            case 9:
            this.QrLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.TxtAddress = ((System.Windows.Controls.TextBox)(target));
            
            #line 57 "..\..\..\PleaseEnterAddress.xaml"
            this.TxtAddress.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtAddress_OnTextChanged);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

