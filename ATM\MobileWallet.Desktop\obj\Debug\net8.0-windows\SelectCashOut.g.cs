﻿#pragma checksum "..\..\..\SelectCashOut.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "A3D3CFCDCD465A8A53FB4C3F8F2AB0B50588DAC0"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MobileWallet.Desktop {
    
    
    /// <summary>
    /// SelectCashOut
    /// </summary>
    public partial class SelectCashOut : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 15 "..\..\..\SelectCashOut.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RowDefinition DynamicHeight;
        
        #line default
        #line hidden
        
        
        #line 22 "..\..\..\SelectCashOut.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectCashInOut;
        
        #line default
        #line hidden
        
        
        #line 26 "..\..\..\SelectCashOut.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border BtnDepositBorder;
        
        #line default
        #line hidden
        
        
        #line 28 "..\..\..\SelectCashOut.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Deposit;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\SelectCashOut.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border BtnWithdrawBorder;
        
        #line default
        #line hidden
        
        
        #line 34 "..\..\..\SelectCashOut.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Withdraw;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\SelectCashOut.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Back;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\SelectCashOut.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Cancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MobileWalletDesk;component/selectcashout.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\SelectCashOut.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 8 "..\..\..\SelectCashOut.xaml"
            ((MobileWallet.Desktop.SelectCashOut)(target)).Closing += new System.ComponentModel.CancelEventHandler(this.SelectCashOut_OnClosing);
            
            #line default
            #line hidden
            
            #line 9 "..\..\..\SelectCashOut.xaml"
            ((MobileWallet.Desktop.SelectCashOut)(target)).Loaded += new System.Windows.RoutedEventHandler(this.SelectCashOut_OnLoaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.DynamicHeight = ((System.Windows.Controls.RowDefinition)(target));
            return;
            case 3:
            this.SelectCashInOut = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.BtnDepositBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 5:
            this.Deposit = ((System.Windows.Controls.Button)(target));
            
            #line 29 "..\..\..\SelectCashOut.xaml"
            this.Deposit.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Deposit);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BtnWithdrawBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 7:
            this.Withdraw = ((System.Windows.Controls.Button)(target));
            
            #line 35 "..\..\..\SelectCashOut.xaml"
            this.Withdraw.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Withdrawl);
            
            #line default
            #line hidden
            return;
            case 8:
            this.Back = ((System.Windows.Controls.Button)(target));
            
            #line 41 "..\..\..\SelectCashOut.xaml"
            this.Back.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Back);
            
            #line default
            #line hidden
            return;
            case 9:
            this.Cancel = ((System.Windows.Controls.Button)(target));
            
            #line 45 "..\..\..\SelectCashOut.xaml"
            this.Cancel.Click += new System.Windows.RoutedEventHandler(this.Button_Click_Cancel);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

