﻿// Decompiled with JetBrains decompiler
// Type: MPOST.PauseClearedEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("A721BE7A-72B8-4570-B803-B802F933D719")]
  [ComVisible(true)]
  public delegate void PauseClearedEventHandler(object sender, EventArgs e);
}
