using System;
using System.Net.NetworkInformation;
using System.Threading;
using System.Threading.Tasks;

namespace MobileWallet.Desktop.Client
{
    /// <summary>
    /// Monitors network connectivity and API endpoint health
    /// </summary>
    public static class ConnectionHealthMonitor
    {
        private static DateTime _lastSuccessfulConnection = DateTime.MinValue;
        private static bool _isConnected = true;
        private static readonly object _lockObject = new object();

        /// <summary>
        /// Gets whether the connection is currently healthy
        /// </summary>
        public static bool IsConnected
        {
            get
            {
                lock (_lockObject)
                {
                    return _isConnected;
                }
            }
            private set
            {
                lock (_lockObject)
                {
                    _isConnected = value;
                }
            }
        }

        /// <summary>
        /// Gets the time of the last successful connection
        /// </summary>
        public static DateTime LastSuccessfulConnection
        {
            get
            {
                lock (_lockObject)
                {
                    return _lastSuccessfulConnection;
                }
            }
        }

        /// <summary>
        /// Records a successful connection
        /// </summary>
        public static void RecordSuccessfulConnection()
        {
            lock (_lockObject)
            {
                _lastSuccessfulConnection = DateTime.Now;
                _isConnected = true;
            }
        }

        /// <summary>
        /// Records a connection failure
        /// </summary>
        public static void RecordConnectionFailure()
        {
            lock (_lockObject)
            {
                _isConnected = false;
            }
        }

        /// <summary>
        /// Checks if the network is available
        /// </summary>
        /// <returns>True if network is available</returns>
        public static bool IsNetworkAvailable()
        {
            try
            {
                return NetworkInterface.GetIsNetworkAvailable();
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Pings the API host to check connectivity
        /// </summary>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>True if ping is successful</returns>
        public static async Task<bool> PingApiHostAsync(int timeout = 5000)
        {
            try
            {
                var uri = new Uri(Global.BaseUrl);
                var ping = new Ping();
                var reply = await ping.SendPingAsync(uri.Host, timeout);
                return reply.Status == IPStatus.Success;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Gets the connection status with additional details
        /// </summary>
        /// <returns>Connection status information</returns>
        public static async Task<ConnectionStatus> GetConnectionStatusAsync()
        {
            var status = new ConnectionStatus
            {
                IsConnected = IsConnected,
                LastSuccessfulConnection = LastSuccessfulConnection,
                IsNetworkAvailable = IsNetworkAvailable()
            };

            if (status.IsNetworkAvailable)
            {
                status.CanPingApiHost = await PingApiHostAsync();
            }

            return status;
        }

        /// <summary>
        /// Waits for network connectivity to be restored
        /// </summary>
        /// <param name="maxWaitTime">Maximum time to wait</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if connectivity is restored within the timeout</returns>
        public static async Task<bool> WaitForConnectivityAsync(TimeSpan maxWaitTime, CancellationToken cancellationToken = default)
        {
            var endTime = DateTime.Now.Add(maxWaitTime);
            
            while (DateTime.Now < endTime && !cancellationToken.IsCancellationRequested)
            {
                if (IsNetworkAvailable() && await PingApiHostAsync())
                {
                    RecordSuccessfulConnection();
                    return true;
                }

                await Task.Delay(1000, cancellationToken);
            }

            return false;
        }
    }

    /// <summary>
    /// Represents the current connection status
    /// </summary>
    public class ConnectionStatus
    {
        public bool IsConnected { get; set; }
        public DateTime LastSuccessfulConnection { get; set; }
        public bool IsNetworkAvailable { get; set; }
        public bool CanPingApiHost { get; set; }

        public override string ToString()
        {
            return $"Connected: {IsConnected}, Network Available: {IsNetworkAvailable}, " +
                   $"Can Ping API: {CanPingApiHost}, Last Success: {LastSuccessfulConnection:yyyy-MM-dd HH:mm:ss}";
        }
    }
}
