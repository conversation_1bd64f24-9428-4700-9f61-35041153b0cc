﻿// Decompiled with JetBrains decompiler
// Type: MPOST.ClearAuditEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("2C727BBF-1D27-41f4-B0E6-A49E3D17D588")]
  [ComVisible(true)]
  public delegate void ClearAuditEventHandler(object sender, ClearAuditEventArgs e);
}
