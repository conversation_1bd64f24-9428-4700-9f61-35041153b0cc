﻿using System.Windows;
using System.Windows.Media.Imaging;
using MobileWallet.Desktop.API;
using MobileWallet.Desktop.Atm;
using MobileWallet.Desktop.Client;
using MobileWallet.Desktop.Extensions;
using ZXing;

namespace MobileWallet.Desktop;

public partial class WithdrawWait : Window
{
    private AppTransactionDto result;
    private string accountNumber;
    private List<CassetteDTO> cassetteDtos;
    private int amount;
    private GetCryptoQuoteResponseModel data;
    private string amountToSend = "";

    public WithdrawWait(
        AppTransactionDto resultData,
        string accountNumber,
        List<CassetteDTO> cassetteDtos,
        int amount,
        GetCryptoQuoteResponseModel data
    )
    {
        InitializeComponent();
        Set_Language();
        this.loaderImage.Source = new Uri(Environment.CurrentDirectory + @"/images/loader1.gif");
        result = resultData;
        this.accountNumber = accountNumber;
        this.cassetteDtos = cassetteDtos;
        this.amount = amount;
        this.data = data;
    }

    private BitmapSource GenerateQrCode(string text)
    {
        // Initialize BarcodeWriter
        var writer = new BarcodeWriterPixelData()
        {
            Format = BarcodeFormat.QR_CODE, // Specify QR Code format
            Options = new ZXing.Common.EncodingOptions
            {
                Height = 300,
                Width = 300,
                Margin =
                    1 // Optional: Adjust margin
                ,
            },
        };

        // Generate the QR Code as a Bitmap
        var qrCodeBitmap = writer.Encode(text);
        // Convert Bitmap to BitmapSource for WPF compatibility
        return qrCodeBitmap.ToBitMapSource();
    }

    public void Set_Language()
    {
        switch (Global.DefaultLanguage)
        {
            case "English":
                TransactionProcessing.Text = ResourceEnglish.TransactionProcessing;
                break;
            case "French":
                TransactionProcessing.Text = ResourceFrench.TransactionProcessing;
                break;
        }
    }

    private void WithdrawWait_OnLoaded(object sender, RoutedEventArgs e)
    {
        BarcodeImage.Source = GenerateQrCode(result.ReceiverAddress);
        amountToSend = data.Quote.ToString("N6");
        TxtAmountToSend.Text =
            "Please send "
            + Global.SelectedToken.Symbol
            + " "
            + amountToSend
            + " to the above Address";
        _ = ProcessWithdraw();
    }

    private async Task ProcessWithdraw()
    {
        var bitCoinClient = new CryptoClient(HttpClientSingleton.Instance);
        var currentTime = DateTime.UtcNow;
        var isTimeOut = false;
        while (true)
        {
            await Task.Delay(30000);
            if (
                (DateTime.UtcNow - currentTime).TotalMinutes
                > Global.CryptoWithdrawWaitTime.TotalMinutes
            )
            {
                isTimeOut = true;
                break;
            }

            var quoteResponse = await bitCoinClient.Crypto_GetCryptoQuoteByIdAsync(
                result.TransactionId,
                TokenManager.SessionId
            );
            var status = quoteResponse.Data;
            if (status.Status.ToUpper() == "SUCCESS")
            {
                break;
            }

            if (status.Status.ToUpper() == "FAILED")
            {
                App.HideProcessingDialog();
                CustomMessageBox.Show("Transaction Failed");
                WelcomeToMobileWallet welcomeToMobileWallet = new WelcomeToMobileWallet();
                welcomeToMobileWallet.Show();
                Close();
                return;
            }
        }

        if (isTimeOut)
        {
            App.HideProcessingDialog();
            CustomMessageBox.Show(
                "Transaction was not completed, please pay within 30 minutes or try again after few minutes"
            );
            var isValid = ReceiptPrinter.IsValid();
            if (isValid == 0)
            {
                double cashIn = amount;
                string transactionId = result.TransactionId;
                string date = DateTime.Now.ToString("yyyy-MM-dd");
                string time = DateTime.Now.ToString("hh:mm tt");
                string currency = "XAF";
                string accountNo = accountNumber;
                bool isSuccess = ReceiptPrinter.PrintWithdrawReceiptCrypto(
                    TokenManager.UserName,
                    Global.SelectedPassPort?.MRZInfo.GivenName ?? "",
                    cashIn,
                    transactionId,
                    date,
                    time,
                    currency,
                    accountNo,
                    data.Quote.ToString("N6"),
                    Global.SelectedToken.Symbol,
                    data.GasFee.ToString("N4"),
                    data.AvgPrice.ToString("N6"),
                    result.ReceiverAddress,
                    Global.UserAddress,
                    result.TxHash,
                    null,
                    $"1 {Global.SelectedNetwork.Symbol} = $" + data.AvgPrice.ToString("F4"),
                    "1 USD = " + data.ExchangeRate + " XAF",
                    "$" + data.TotalUsd,
                    "PENDING",
                    result.ExternalTransactionId
                );
            }

            WelcomeToMobileWallet welcomeToMobileWallet = new WelcomeToMobileWallet();
            welcomeToMobileWallet.Show();
            Close();
            return;
        }

        List<ReceiptItem> item = await WithdrawHelper.Withdraw(amount, cassetteDtos);
        if (item.Any())
        {
            double cashIn = amount;
            string transactionId = result.TransactionId;
            string date = DateTime.Now.ToString("yyyy-MM-dd");
            string time = DateTime.Now.ToString("hh:mm tt");
            string currency = Global.Currency;
            string accountNo = accountNumber;
            var isValid = ReceiptPrinter.IsValid();
            _ = App.TrackAtmRealTime(
                new UpdateAtmRealTimeRequestModel() { PrintStatus = isValid.ToString() }
            );
            if (Global.UseHardware && isValid == 0)
            {
                bool isSuccess = ReceiptPrinter.PrintWithdrawReceiptCrypto(
                    TokenManager.UserName,
                    Global.SelectedPassPort?.MRZInfo.GivenName ?? "",
                    cashIn,
                    transactionId,
                    date,
                    time,
                    currency,
                    accountNo,
                    data.Quote.ToString("N6"),
                    Global.SelectedToken.Symbol,
                    data.GasFee.ToString("N4"),
                    data.Quote.ToString("N6"),
                    result.ReceiverAddress,
                    Global.UserAddress,
                    result.TxHash,
                    item,
                    $"1 {Global.SelectedNetwork.Symbol} = $" + data.AvgPrice.ToString("F4"),
                    "1 USD = " + data.ExchangeRate + " " + Global.Currency,
                    "$" + data.TotalUsd,
                    "CONFIRMED",
                    result.ExternalTransactionId
                );
                _ = App.LogError("Withdraw Print Completed", LogType.Withdraw, "");
            }
            else if (Global.UseHardware)
            {
                _ = App.LogError("Printer Error", LogType.Error, "Error Code: " + isValid);
                CustomMessageBox.Show("Receipt is not available at the moment");
            }

            if (true)
            {
                await bitCoinClient.Crypto_RedeemQuoteAsync(result.TransactionId);
                App.HideProcessingDialog();
                // CustomMessageBox.ShowDialog(
                //     Global.IsFrench
                //         ? "Veuillez recuperer vos billets !!"
                //         : "Please collect your money!!"
                // );
                ThankYou thankYou = new ThankYou();
                thankYou.Show();
                this.Close();
            }
        }
    }
}
