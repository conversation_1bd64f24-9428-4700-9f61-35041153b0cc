using System;
using System.Threading.Tasks;
using MobileWallet.Desktop.API;

namespace MobileWallet.Desktop.Client
{
    /// <summary>
    /// Test class to verify connection resilience improvements
    /// </summary>
    public static class ConnectionResilienceTest
    {
        /// <summary>
        /// Tests the connection resilience by making a simple API call
        /// </summary>
        /// <returns>Test result information</returns>
        public static async Task<TestResult> TestConnectionResilienceAsync()
        {
            var result = new TestResult
            {
                StartTime = DateTime.Now
            };

            try
            {
                App.AppLogger?.Info("Starting connection resilience test...");

                // Test 1: Check network availability
                result.IsNetworkAvailable = ConnectionHealthMonitor.IsNetworkAvailable();
                App.AppLogger?.Info($"Network available: {result.IsNetworkAvailable}");

                // Test 2: Ping API host
                result.CanPingApiHost = await ConnectionHealthMonitor.PingApiHostAsync();
                App.AppLogger?.Info($"Can ping API host: {result.CanPingApiHost}");

                // Test 3: Get connection status
                var connectionStatus = await ConnectionHealthMonitor.GetConnectionStatusAsync();
                result.ConnectionStatus = connectionStatus.ToString();
                App.AppLogger?.Info($"Connection status: {connectionStatus}");

                // Test 4: Try to create a session (this will test the retry logic)
                try
                {
                    var sessionClient = new SessionClient(HttpClientSingleton.Instance);
                    var sessionResponse = await sessionClient.Session_CreateSessionAsync();
                    result.SessionCreated = sessionResponse?.Data != null;
                    result.SessionId = sessionResponse?.Data;
                    App.AppLogger?.Info($"Session created: {result.SessionCreated}, ID: {result.SessionId}");
                }
                catch (Exception ex)
                {
                    result.SessionCreated = false;
                    result.SessionError = ex.Message;
                    App.AppLogger?.Error(ex, "Failed to create session during test");
                }

                result.Success = result.IsNetworkAvailable && result.CanPingApiHost;
                result.EndTime = DateTime.Now;
                result.Duration = result.EndTime - result.StartTime;

                App.AppLogger?.Info($"Connection resilience test completed. Success: {result.Success}, Duration: {result.Duration.TotalSeconds:F2}s");
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Error = ex.Message;
                result.EndTime = DateTime.Now;
                result.Duration = result.EndTime - result.StartTime;
                App.AppLogger?.Error(ex, "Connection resilience test failed");
            }

            return result;
        }

        /// <summary>
        /// Runs a continuous connectivity test for monitoring purposes
        /// </summary>
        /// <param name="durationMinutes">How long to run the test</param>
        /// <param name="intervalSeconds">Interval between checks</param>
        /// <returns>Summary of the test results</returns>
        public static async Task<ContinuousTestResult> RunContinuousConnectivityTestAsync(int durationMinutes = 5, int intervalSeconds = 30)
        {
            var result = new ContinuousTestResult
            {
                StartTime = DateTime.Now,
                DurationMinutes = durationMinutes,
                IntervalSeconds = intervalSeconds
            };

            var endTime = DateTime.Now.AddMinutes(durationMinutes);
            
            App.AppLogger?.Info($"Starting continuous connectivity test for {durationMinutes} minutes with {intervalSeconds}s intervals");

            while (DateTime.Now < endTime)
            {
                try
                {
                    var testResult = await TestConnectionResilienceAsync();
                    result.TotalTests++;
                    
                    if (testResult.Success)
                    {
                        result.SuccessfulTests++;
                    }
                    else
                    {
                        result.FailedTests++;
                    }

                    await Task.Delay(TimeSpan.FromSeconds(intervalSeconds));
                }
                catch (Exception ex)
                {
                    result.FailedTests++;
                    App.AppLogger?.Error(ex, "Error during continuous connectivity test");
                }
            }

            result.EndTime = DateTime.Now;
            result.SuccessRate = result.TotalTests > 0 ? (double)result.SuccessfulTests / result.TotalTests * 100 : 0;

            App.AppLogger?.Info($"Continuous connectivity test completed. Success rate: {result.SuccessRate:F1}% ({result.SuccessfulTests}/{result.TotalTests})");

            return result;
        }
    }

    /// <summary>
    /// Result of a single connection resilience test
    /// </summary>
    public class TestResult
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public bool Success { get; set; }
        public string? Error { get; set; }
        public bool IsNetworkAvailable { get; set; }
        public bool CanPingApiHost { get; set; }
        public string? ConnectionStatus { get; set; }
        public bool SessionCreated { get; set; }
        public string? SessionId { get; set; }
        public string? SessionError { get; set; }

        public override string ToString()
        {
            return $"Test Result - Success: {Success}, Duration: {Duration.TotalSeconds:F2}s, " +
                   $"Network: {IsNetworkAvailable}, Ping: {CanPingApiHost}, Session: {SessionCreated}";
        }
    }

    /// <summary>
    /// Result of a continuous connectivity test
    /// </summary>
    public class ContinuousTestResult
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public int DurationMinutes { get; set; }
        public int IntervalSeconds { get; set; }
        public int TotalTests { get; set; }
        public int SuccessfulTests { get; set; }
        public int FailedTests { get; set; }
        public double SuccessRate { get; set; }

        public override string ToString()
        {
            return $"Continuous Test Result - Duration: {DurationMinutes}min, " +
                   $"Tests: {TotalTests}, Success Rate: {SuccessRate:F1}% ({SuccessfulTests}/{TotalTests})";
        }
    }
}
