﻿using System.Windows;
using MobileWallet.Desktop.API;

namespace MobileWallet.Desktop
{
    /// <summary>
    /// Interaction logic for ThankYou.xaml
    /// </summary>
    public partial class ThankYou : Window
    {
        System.Windows.Threading.DispatcherTimer dispatcherTimer = new System.Windows.Threading.DispatcherTimer();
        public ThankYou()
        {
            InitializeComponent();
            Set_Language();
            dispatcherTimer.Tick += new EventHandler(dispatcherTimer_Tick);
            dispatcherTimer.Interval = new TimeSpan(0, 0, 10);
            dispatcherTimer.Start();
        }
        private void Set_Language()
        {
            switch (Global.DefaultLanguage)
            {
                case "English":

                    Thanks.Text = ResourceEnglish.Thanks;

                    break;
                case "French":

                    Thanks.Text = ResourceFrench.Thanks;

                    break;
            }

        }
        private void dispatcherTimer_Tick(object sender, EventArgs e)
        {
            WelcomeToMobileWallet welcomeToMobileWallet = new WelcomeToMobileWallet();
            welcomeToMobileWallet.Show();
            dispatcherTimer.Stop();
            this.Close();

        }

        private void ThankYou_OnLoaded(object sender, RoutedEventArgs e)
        {
            _ = App.TrackAtmRealTime(new UpdateAtmRealTimeRequestModel()
            {
                CurrentScreen = nameof(ThankYou)
            });
        }
    }
}
