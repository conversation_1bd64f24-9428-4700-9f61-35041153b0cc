﻿#pragma checksum "..\..\..\Window7.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DE6150512AEB0B9758FD797D5254EC422BDE6E0E"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MobileWallet.Desktop {
    
    
    /// <summary>
    /// Window7
    /// </summary>
    public partial class Window7 : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 19 "..\..\..\Window7.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtotp;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\Window7.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtAccountNumber;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\Window7.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid numbperpad;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\Window7.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn1;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\Window7.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn2;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\Window7.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn3;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\Window7.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn4;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\Window7.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn5;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\Window7.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn6;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\Window7.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn7;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\Window7.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn8;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\Window7.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn9;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\Window7.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnclear;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\Window7.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn0;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\Window7.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btndone;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MobileWallet.Desktop;V1.0.0.0;component/window7.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\Window7.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.txtotp = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            
            #line 24 "..\..\..\Window7.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Button_Click_Submit);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 29 "..\..\..\Window7.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Button_Click_Resendotp);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 34 "..\..\..\Window7.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Button_Click_Back);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 38 "..\..\..\Window7.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Button_Click_Cancel);
            
            #line default
            #line hidden
            return;
            case 6:
            this.txtAccountNumber = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.numbperpad = ((System.Windows.Controls.Grid)(target));
            return;
            case 8:
            this.btn1 = ((System.Windows.Controls.Button)(target));
            
            #line 58 "..\..\..\Window7.xaml"
            this.btn1.Click += new System.Windows.RoutedEventHandler(this.btn1_Click_1);
            
            #line default
            #line hidden
            return;
            case 9:
            this.btn2 = ((System.Windows.Controls.Button)(target));
            
            #line 59 "..\..\..\Window7.xaml"
            this.btn2.Click += new System.Windows.RoutedEventHandler(this.btn2_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.btn3 = ((System.Windows.Controls.Button)(target));
            
            #line 60 "..\..\..\Window7.xaml"
            this.btn3.Click += new System.Windows.RoutedEventHandler(this.btn3_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.btn4 = ((System.Windows.Controls.Button)(target));
            
            #line 62 "..\..\..\Window7.xaml"
            this.btn4.Click += new System.Windows.RoutedEventHandler(this.btn4_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.btn5 = ((System.Windows.Controls.Button)(target));
            
            #line 63 "..\..\..\Window7.xaml"
            this.btn5.Click += new System.Windows.RoutedEventHandler(this.btn5_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.btn6 = ((System.Windows.Controls.Button)(target));
            
            #line 64 "..\..\..\Window7.xaml"
            this.btn6.Click += new System.Windows.RoutedEventHandler(this.btn6_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.btn7 = ((System.Windows.Controls.Button)(target));
            
            #line 66 "..\..\..\Window7.xaml"
            this.btn7.Click += new System.Windows.RoutedEventHandler(this.btn7_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.btn8 = ((System.Windows.Controls.Button)(target));
            
            #line 67 "..\..\..\Window7.xaml"
            this.btn8.Click += new System.Windows.RoutedEventHandler(this.btn8_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.btn9 = ((System.Windows.Controls.Button)(target));
            
            #line 68 "..\..\..\Window7.xaml"
            this.btn9.Click += new System.Windows.RoutedEventHandler(this.btn9_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.btnclear = ((System.Windows.Controls.Button)(target));
            
            #line 70 "..\..\..\Window7.xaml"
            this.btnclear.Click += new System.Windows.RoutedEventHandler(this.btnclear_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.btn0 = ((System.Windows.Controls.Button)(target));
            
            #line 71 "..\..\..\Window7.xaml"
            this.btn0.Click += new System.Windows.RoutedEventHandler(this.btn0_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.btndone = ((System.Windows.Controls.Button)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

