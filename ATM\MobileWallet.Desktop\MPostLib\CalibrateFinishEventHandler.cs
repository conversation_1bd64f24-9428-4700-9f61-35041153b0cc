﻿// Decompiled with JetBrains decompiler
// Type: MPOST.CalibrateFinishEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("F61408A4-4C22-4335-81A6-F572363C45B0")]
  [ComVisible(true)]
  public delegate void CalibrateFinishEventHandler(object sender, EventArgs e);
}
