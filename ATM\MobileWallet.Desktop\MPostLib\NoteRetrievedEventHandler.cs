﻿// Decompiled with JetBrains decompiler
// Type: MPOST.NoteRetrievedEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("7DB8545E-DA8C-4cda-9580-B5CE78CAA75F")]
  [ComVisible(true)]
  public delegate void NoteRetrievedEventHandler(object sender, EventArgs e);
}
