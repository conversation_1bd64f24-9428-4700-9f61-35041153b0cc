﻿// Decompiled with JetBrains decompiler
// Type: MPOST.RejectedEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("C7D2B34A-816A-4571-BACF-2CA26D54326C")]
  [ComVisible(true)]
  public delegate void RejectedEventHandler(object sender, EventArgs e);
}
