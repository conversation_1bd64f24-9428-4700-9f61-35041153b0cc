﻿using System.ComponentModel;
using System.Windows;
using System.Windows.Threading;
using Emgu.CV;
using Emgu.CV.CvEnum;
using MobileWallet.Desktop.API;
using MobileWallet.Desktop.Atm;
using MobileWallet.Desktop.Client;
using MobileWallet.Desktop.Extensions;
using MobileWallet.Desktop.Helpers;

namespace MobileWallet.Desktop;

public partial class PleaseEnterTransactionId : Window
{
    public PleaseEnterTransactionId()
    {
        InitializeComponent();
        SetLanguage();
    }

    public void SetLanguage()
    {
        if (Global.IsFrench)
        {
            WindowTitle.Text = ResourceFrench.PlaceYourQrCodeAlignWithTheCamera;
        }
        else
        {
            WindowTitle.Text = ResourceEnglish.PlaceYourQrCodeAlignWithTheCamera;
        }
    }

    private void Button_Click_Cancel(object sender, RoutedEventArgs e)
    {
        WelcomeToMobileWallet window = new WelcomeToMobileWallet();
        window.Show();
        this.Close();
    }

    private void Button_Click(object sender, RoutedEventArgs e)
    {
        SelectCryptoCashInCashOut window = new SelectCryptoCashInCashOut();
        window.Show();
        Close();
    }

    private async void Button_Click_Submit(object sender, RoutedEventArgs e)
    {
        try
        {
            ButtonHelper.ToggleButton(sender);
            App.ShowProcessingDialog();
            var cryptoClient = new CryptoClient(HttpClientSingleton.Instance);
            var data = await cryptoClient.Crypto_GetCryptoQuoteByIdAsync(
                TxtTransactionId.Text,
                TokenManager.SessionId
            );
            if (data != null)
            {
                if (data.Data.Status == "Pending")
                {
                    App.HideProcessingDialog();
                    CustomMessageBox.Show(
                        "Transaction is not completed yet. Please try again later"
                    );
                    return;
                }

                if (data.Data.Status == "Failed")
                {
                    App.HideProcessingDialog();
                    CustomMessageBox.Show("Transaction has been failed.");
                    return;
                }

                var amount = data.Data.Amount;
                if (data.Data.IsRedeemed)
                {
                    CustomMessageBox.Show("This Transaction has already been Redeemed");
                    return;
                }

                var status = App.Dispenser.ReadCassetteStatus();
                if (!await WithdrawHelper.IsAvailableCurrency((int)amount, status))
                {
                    CustomMessageBox.Show("Notes Not Available for XAF " + amount);
                    return;
                }
                _ = App.TrackAtmRealTime(new UpdateAtmRealTimeRequestModel() { IsWithdraw = true });
                var item = await WithdrawHelper.Withdraw((int)amount, status);
                if (item.Any())
                {
                    await cryptoClient.Crypto_RedeemQuoteAsync(TxtTransactionId.Text);
                    if (true)
                    {
                        App.HideProcessingDialog();
                        CustomMessageBox.ShowDialog(
                            Global.IsFrench
                                ? "Veuillez recuperer vos billets !!"
                                : "Please collect your money!!"
                        );
                        ThankYou thankYou = new ThankYou();
                        thankYou.Show();
                        this.Close();
                    }
                }
            }
        }
        catch (Exception exception)
        {
            App.AppLogger.Error(exception, exception.Message);
            App.HideProcessingDialog();
            CustomMessageBox.Show("No Transaction to Redeem");
        }
        finally
        {
            ButtonHelper.ToggleButton(sender);
        }
    }

    private void PleaseEnterTransactionId_OnLoaded(object sender, RoutedEventArgs e)
    {
        Helper.AdjustRowHeight(this, DynamicRow, 300);
        App.StartTimer(this);
        _ = App.TrackAtmRealTime(
            new UpdateAtmRealTimeRequestModel() { CurrentScreen = nameof(PleaseEnterTransactionId) }
        );
        TxtTransactionId.Focus();
    }

    private void PleaseEnterTransactionId_OnClosing(object? sender, CancelEventArgs e)
    {
        App.StopTimer();
    }
}
