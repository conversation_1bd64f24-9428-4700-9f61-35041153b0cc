﻿using System.Diagnostics;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using Emgu.CV;
using ITL;
using MobileWallet.Desktop.API;
using MobileWallet.Desktop.Atm;
using MobileWallet.Desktop.Client;
using MobileWallet.Desktop.MPostLib;
using NLog;
using Python.Runtime;

namespace MobileWallet.Desktop
{
    public class Helper
    {
        public static void AdjustRowHeight(
            Window window,
            RowDefinition rowDefinition,
            int height = 500,
            int star = 3
        )
        {
            // Check the window's current height and adjust the row height
            if (window.ActualWidth > window.ActualHeight) // Landscape
            {
                rowDefinition.Height = new GridLength(height, GridUnitType.Pixel); // Dynamic height for landscape
            }
            else
            {
                rowDefinition.Height = new GridLength(star, GridUnitType.Star); // Dynamic height for landscape
            }
        }

        public string GetReason(string? reasonCode)
        {
            switch (reasonCode)
            {
                case "NOT_ENOUGH_FUNDS":
                    {
                        if (Global.IsFrench)
                        {
                            return "Vous n'avez pas assez de fonds sur votre compte";
                        }
                        return "You don't have enough funds in your account";
                    }
                    break;
                case "COULD_NOT_PERFORM_TRANSACTION":
                    {
                        if (Global.IsFrench)
                        {
                            return "Transaction échouée. Veuillez vérifier que vous disposez de fonds suffisants sur votre compte. Veuillez réessayer plus tard.";
                        }
                        return "Transaction failed. Please make sure you have enough funds in your account. Please try again later.";
                    }
                    break;
            }
            return reasonCode ?? "";
        }

        public void CloseWindow(App app, Window? window)
        {
            app.Dispatcher.Invoke(() =>
            {
                WelcomeToMobileWallet NewWindow = new WelcomeToMobileWallet();
                NewWindow.Show();
                window?.Close();
            });
        }
    }

    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        static DispatcherTimer timer = new DispatcherTimer();
        static DispatcherTimer sessionKeepAliveTimer = new DispatcherTimer();
        private static Window? _currentWindow;
        private static DateTime _lastUserActivity = DateTime.Now;
        private static bool _isInTransaction = false;
        public static App Instance;
        public static readonly Logger AppLogger = LogManager.GetCurrentClassLogger();

        // Configurable timeout values
        private static readonly TimeSpan IdleTimeout = TimeSpan.FromMinutes(2); // Reduced from 60 seconds to 2 minutes
        private static readonly TimeSpan SessionKeepAliveInterval = TimeSpan.FromMinutes(5); // Keep session alive every 5 minutes
        private static readonly TimeSpan TransactionTimeout = TimeSpan.FromMinutes(10); // Extended timeout during transactions

        public static void StartTimer(Window window)
        {
            _currentWindow = window;
            _lastUserActivity = DateTime.Now;
            _isInTransaction = IsTransactionWindow(window);
            timer.Start();

            AppLogger?.Info($"Timer started for window: {window.GetType().Name}, IsTransaction: {_isInTransaction}");
        }

        public static void StopTimer()
        {
            Instance.Dispatcher.Invoke(() =>
            {
                _currentWindow = null;
                _isInTransaction = false;
                timer.Stop();
                AppLogger?.Info("Timer stopped");
            });
        }

        public static async void RefreshUserActivity()
        {
            var wasIdle = (DateTime.Now - _lastUserActivity).TotalMinutes > 5;
            _lastUserActivity = DateTime.Now;

            if (wasIdle)
            {
                AppLogger?.Info("User returned from idle state, reactivating devices");
                await HardwareDeviceManager.ReactivateDevicesFromIdle();
            }

            AppLogger?.Debug("User activity refreshed");
        }

        private static bool IsTransactionWindow(Window window)
        {
            var transactionWindows = new[]
            {
                "PleaseInsertNote", "PleaseInsertNoteV2", "WithdrawWait", "WithdrawWaitV2",
                "ProcessingScreen", "SecureOTPhasbeensent", "PleaseEnterTransactionId",
                "PleaseEnterTransactionIdV2", "CapturePassport", "CapturePassportV2"
            };

            return transactionWindows.Any(name => window.GetType().Name.Contains(name));
        }

        private void TimerOnElapsed(object? sender, EventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                var timeSinceActivity = DateTime.Now - _lastUserActivity;
                var timeoutToUse = _isInTransaction ? TransactionTimeout : IdleTimeout;

                if (timeSinceActivity >= timeoutToUse)
                {
                    AppLogger?.Info($"Idle timeout reached. Time since activity: {timeSinceActivity}, Timeout: {timeoutToUse}, IsTransaction: {_isInTransaction}");

                    // Graceful cleanup before returning to welcome screen
                    try
                    {
                        CleanupBeforeTimeout();
                        new Helper().CloseWindow(this, _currentWindow);
                        StopTimer();
                    }
                    catch (Exception ex)
                    {
                        AppLogger?.Error(ex, "Error during timeout cleanup");
                    }
                }
            });
        }

        private async void CleanupBeforeTimeout()
        {
            try
            {
                // Prepare devices for idle state
                await HardwareDeviceManager.PrepareDevicesForIdle();

                // Stop any ongoing hardware operations
                if (_cashDevice != null)
                {
                    _cashDevice.DisableAcceptor();
                }

                // Hide processing dialogs
                HideProcessingDialog();

                AppLogger?.Info("Cleanup completed before timeout");
            }
            catch (Exception ex)
            {
                AppLogger?.Error(ex, "Error during cleanup");
            }
        }

        private async void SessionKeepAliveTimer_Tick(object? sender, EventArgs e)
        {
            try
            {
                // Only refresh token if we have one and it's close to expiry
                if (!string.IsNullOrEmpty(TokenManager.AccessToken))
                {
                    var timeUntilExpiry = TokenManager.Expiry - DateTime.Now;

                    // Refresh token if it expires within 10 minutes
                    if (timeUntilExpiry.TotalMinutes <= 10)
                    {
                        AppLogger?.Info($"Proactively refreshing token. Time until expiry: {timeUntilExpiry}");

                        var authClient = new AppAuthClient();
                        var refreshSuccess = await authClient.RefreshToken(TokenManager.RefreshToken);

                        if (refreshSuccess)
                        {
                            AppLogger?.Info("Proactive token refresh successful");

                            // Refresh SignalR connection with new token
                            try
                            {
                                await SignalRClient.StopConnectionAsync();
                                await SignalRClient.InitializeConnectionAsync(TokenManager.AccessToken);
                                AppLogger?.Info("SignalR connection refreshed with new token");
                            }
                            catch (Exception signalREx)
                            {
                                AppLogger?.Error(signalREx, "Failed to refresh SignalR connection");
                            }
                        }
                        else
                        {
                            AppLogger?.Error("Proactive token refresh failed");
                        }
                    }
                }

                // Send keep-alive ping to maintain connection
                var connectionStatus = await ConnectionHealthMonitor.GetConnectionStatusAsync();
                AppLogger?.Debug($"Session keep-alive check - Connection status: {connectionStatus}");
            }
            catch (Exception ex)
            {
                AppLogger?.Error(ex, "Error in session keep-alive timer");
            }
        }

        public static void DisposeOnlyCashAcceptor()
        {
            _cashAcceptor?.close_acceptor();
            _cashAcceptor = null;
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
        }

        public static void DisposeAll()
        {
            _processingDialog = null;
            _passport = null;
            _camera = null;
            _dispenser = null;
            DisposeOnlyCashAcceptor();
        }

        private static ITL.CashDevice? _cashDevice;
        private static ProcessingScreen? _processingDialog;
        private static PassportReaderV2? _passport;
        private static PassportReaderV3? _passportV2;
        private static Camera? _camera;
        private static PythonDispenser? _dispenser;
        private static CashAcceptor? _cashAcceptor;
        private static SignalRClient? _signalRClient;

        public static CashDevice CashDevice
        {
            get
            {
                if (_cashDevice == null)
                {
                    _cashDevice = new CashDevice();
                }
                return _cashDevice;
            }
        }

        public static SignalRClient SignalRClient
        {
            get
            {
                if (_signalRClient != null)
                {
                    return _signalRClient;
                }
                _signalRClient = new SignalRClient();
                return _signalRClient;
            }
        }
        public static CashAcceptor CashAcceptor
        {
            get
            {
                if (_cashAcceptor != null)
                {
                    return _cashAcceptor;
                }
                _cashAcceptor = new CashAcceptor();
                return _cashAcceptor;
            }
        }
        public static PythonDispenser Dispenser
        {
            get
            {
                if (_dispenser != null)
                {
                    return _dispenser;
                }
                _dispenser = new PythonDispenser();
                return _dispenser;
            }
        }
        public static PassportReaderV2 Passport
        {
            get
            {
                if (_passport != null)
                {
                    return _passport;
                }
                _passport = new PassportReaderV2();
                return _passport;
            }
        }
        public static PassportReaderV3 PassportV2
        {
            get
            {
                if (_passportV2 != null)
                {
                    return _passportV2;
                }
                _passportV2 = new PassportReaderV3();
                return _passportV2;
            }
        }
        public static Camera Camera
        {
            get
            {
                if (_camera != null)
                {
                    return _camera;
                }
                _camera = new Camera();
                return _camera;
            }
        }

        public static async Task LogError(
            string message,
            LogType logType = LogType.Error,
            string? error = null
        )
        {
            try
            {
                AppLogger.Debug(message);
                var log = new AppLogClient(HttpClientSingleton.Instance);
                // Ensure title doesn't exceed 50 characters to avoid API validation error
                var title = message.Length > 50 ? message.Substring(0, 47) + "..." : message;

                await log.AppLog_CreateAppLogAsync(
                    new CreateAppLogRequestModel()
                    {
                        Message = message,
                        Data = error,
                        AtmId = TokenManager.UserId,
                        SessionId = TokenManager.SessionId,
                        LogType = logType,
                        Description = message,
                        Title = title,
                        IsCreateTicket = logType == LogType.Error,
                    }
                );
            }
            catch (Exception e)
            {
                AppLogger.Error(e, e.Message);
            }
        }

        public static bool SaveCameraImage()
        {
            try
            {
                var index = Global.CameraIndex;
                using var capture = new VideoCapture(index, VideoCapture.API.DShow);
                if (!capture.IsOpened)
                {
                    Console.WriteLine("Camera not found!");
                    return false;
                }

                // Capture a single frame
                using Mat frame = new Mat();
                capture.Read(frame);
                if (!frame.IsEmpty)
                {
                    string filePath = "captured_image.jpg";
                    frame.Save(filePath);
                    return true;
                }
            }
            catch (Exception e)
            {
                AppLogger.Error(e, e.Message);
                //ignored
            }
            return false;
        }

        public static async Task<bool> UploadPassportInfoIfExist()
        {
            try
            {
                var pass = Global.SelectedPassPort?.MRZInfo ?? null;
                if (pass != null)
                {
                    await new KycClient(HttpClientSingleton.Instance).Kyc_CreateKycAsync(
                        new CreateAppSessionKycRequestModel()
                        {
                            Nationality = pass.Value.Nationality,
                            Sex = pass?.Sex,
                            Surname = pass?.Surname,
                            BirthDate = pass?.BirthDate,
                            DocumentNo = pass?.DocumentNo,
                            DocumentType = pass?.DocumentType,
                            ExpiryDate = pass?.ExpiryDate,
                            GivenName = pass?.GivenName,
                            IssueDate = pass?.IssueDate,
                            IssuePlace = pass?.IssuePlace,
                            SessionId = TokenManager.SessionId,
                        }
                    );
                    return true;
                }

                var passV2 = Global.SelectedPassPortV2?.tPasInfo ?? null;
                if (passV2 != null)
                {
                    await new KycClient(HttpClientSingleton.Instance).Kyc_CreateKycAsync(
                        new CreateAppSessionKycRequestModel()
                        {
                            Nationality = passV2.Value.Nationality,
                            Sex = passV2?.Sex,
                            Surname = passV2?.Surname,
                            BirthDate = passV2?.BirthDate,
                            DocumentNo = passV2?.PassportNo,
                            DocumentType = "Passport",
                            ExpiryDate = passV2?.ExpiryDate,
                            GivenName = passV2?.GivenName,
                            IssueDate = passV2?.IssueDate,
                            IssuePlace = passV2?.IssuePlace,
                            SessionId = TokenManager.SessionId,
                        }
                    );
                    return true;
                }
            }
            catch (Exception e)
            {
                _ = LogError(e.Message, error: e.StackTrace);
            }
            return false;
        }

        public static async Task<bool> UploadCameraPictureIfExist()
        {
            string filePath = "captured_image.jpg";
            try
            {
                if (File.Exists(filePath))
                {
                    byte[] imageBytes = await File.ReadAllBytesAsync(filePath);
                    var base64 = Convert.ToBase64String(imageBytes);
                    await new ImageClient(HttpClientSingleton.Instance).Image_CreateImageAsync(
                        new CreateAppSessionImageRequestModel()
                        {
                            SessionId = TokenManager.SessionId,
                            Image = base64,
                        }
                    );
                    if (File.Exists(filePath))
                    {
                        File.Delete(filePath);
                    }

                    return true;
                }
            }
            catch (Exception e)
            {
                _ = LogError(e.Message, error: e.StackTrace);
            }
            return false;
        }

        public static async Task<bool> UploadCameraPicture(int index = 1)
        {
            try
            {
                if (index == 1)
                {
                    index = Global.CameraIndex;
                }
                using var capture = new VideoCapture(index, VideoCapture.API.DShow);
                if (!capture.IsOpened)
                {
                    Console.WriteLine("Camera not found!");
                    return false;
                }

                // Capture a single frame
                using Mat frame = new Mat();
                capture.Read(frame);

                if (!frame.IsEmpty)
                {
                    // Save the image to file
                    string filePath = "captured_image.jpg";
                    frame.Save(filePath);
                    byte[] imageBytes = await File.ReadAllBytesAsync(filePath);
                    var base64 = Convert.ToBase64String(imageBytes);
                    await new ImageClient(HttpClientSingleton.Instance).Image_CreateImageAsync(
                        new CreateAppSessionImageRequestModel()
                        {
                            SessionId = TokenManager.SessionId,
                            Image = base64,
                        }
                    );
                    if (File.Exists(filePath))
                    {
                        File.Delete(filePath);
                    }
                    return true;
                }
            }
            catch (Exception e)
            {
                _ = LogError(e.Message, error: e.StackTrace);
            }

            return false;
        }

        public static async Task<bool> TrackAtmRealTime(UpdateAtmRealTimeRequestModel model)
        {
            try
            {
                await new RealTimeClient(
                    HttpClientSingleton.Instance
                ).RealTime_UpdateAtmRealTimeAsync(model);
                return true;
            }
            catch (API.ApiException apiEx) when (apiEx.StatusCode == 401)
            {
                App.AppLogger?.Info("TrackAtmRealTime received 401 - token may be expired. Retry will be handled by AuthenticatedHttpClientHandler.");
                // The retry logic in AuthenticatedHttpClientHandler will handle token refresh
                return false;
            }
            catch (Exception e)
            {
                App.AppLogger?.Error(e, $"TrackAtmRealTime failed: {e.Message}");
                return false;
            }
        }

        public static void ShowProcessingDialog()
        {
            HideProcessingDialog();
            _processingDialog ??= new ProcessingScreen();
            _processingDialog.Show();
        }

        public static void HideProcessingDialog()
        {
            if (_processingDialog != null)
            {
                _processingDialog.Hide();
                _processingDialog.Close();
                _processingDialog = null;
            }
        }

        public App()
        {
            LogManager.Setup().LoadConfigurationFromFile("NLog.config");
            // Handle non-UI thread exceptions
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
            // Handle UI thread exceptions
            this.DispatcherUnhandledException += App_DispatcherUnhandledException;

            // Configure idle timeout timer
            timer.Interval = TimeSpan.FromSeconds(30); // Check every 30 seconds instead of 60
            timer.Tick += TimerOnElapsed;

            // Configure session keep-alive timer
            sessionKeepAliveTimer.Interval = SessionKeepAliveInterval;
            sessionKeepAliveTimer.Tick += SessionKeepAliveTimer_Tick;
            sessionKeepAliveTimer.Start(); // Start immediately

            // Initialize hardware device manager
            HardwareDeviceManager.Initialize();
            if (!Global.UseV2)
            {
                try
                {
                    // throw new Exception("TEST");
                    PythonEngine.Initialize();
                }
                catch (Exception e)
                {
                    AppLogger.Error(e, e.Message);
                }
            }
            Instance = this;

            // Initialize connection health monitoring
            _ = Task.Run(async () =>
            {
                try
                {
                    var connectionStatus = await ConnectionHealthMonitor.GetConnectionStatusAsync();
                    AppLogger?.Info($"Application startup - Connection status: {connectionStatus}");
                }
                catch (Exception ex)
                {
                    AppLogger?.Error(ex, "Failed to check initial connection status");
                }
            });
        }

        public static void CloseKeyBoard()
        {
            try
            {
                var processes = Process.GetProcessesByName("osk");
                foreach (var process in processes)
                {
                    process.Kill(); // Close the on-screen keyboard when the TextBox loses focus
                }
            }
            catch (Exception ex)
            {
                _ = LogError($"Failed to close on-screen keyboard: {ex.Message}");
            }
        }

        public static void OpenKeyBoard()
        {
            try
            {
                Process.Start("osk.exe");
            }
            catch (Exception ex)
            {
                _ = LogError($"Failed to start on-screen keyboard: {ex.Message}", LogType.Error);
            }
        }

        private void App_DispatcherUnhandledException(
            object sender,
            System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e
        )
        {
            // Handle the exception
            if (e != null)
            {
                AppLogger.Error(e.Exception, e.Exception.Message);
                HandleException(e.Exception);
                e.Handled = true;
            }
            // Prevent default unhandled exception processing
        }

        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            // Handle the exception
            if (e.ExceptionObject is Exception ex)
            {
                AppLogger.Error(ex, ex.Message);
                HandleException(ex);
            }
        }

        private void HandleException(Exception ex)
        {
            // Log the exception
            // Display a friendly message to the user
            HideProcessingDialog();
            _ = LogError($"An unexpected error occurred: {ex.Message}");
            CustomMessageBox.ShowDialog(
                "An unexpected error occurred. Please contact support.",
                "Error"
            );
            // Optionally, shutdown the application
            // Application.Current.Shutdown();
        }

        private void App_OnExit(object sender, ExitEventArgs e)
        {
            PythonEngine.Shutdown();
        }

        public static async Task InitSignalR()
        {
            await SignalRClient.InitializeConnectionAsync(TokenManager.AccessToken);
            SignalRClient.OnMessageReceived("OnFetchWithdrawInfo", OnFetchWithdrawInfo);
            SignalRClient.OnMessageReceived("OnFetchCameraInfo", OnFetchCameraInfo);
            SignalRClient.OnMessageReceived("OnFetchPrinterInfo", OnFetchPrinterInfo);
            SignalRClient.OnMessageReceived("OnFetchDepositInfo", OnFetchDepositInfo);
            SignalRClient.OnMessageReceived("OnFetchPassportInfo", OnFetchPassportReaderInfo);
        }

        private static void OnFetchPassportReaderInfo(object obj)
        {
            try
            {
                if (!Global.UseHardware)
                {
                    _ = SignalRClient.SendMessageAsync(
                        "SendPassportInfo",
                        obj?.ToString() ?? "",
                        "Open Result: " + (true ? "Open" : "Close")
                    );
                    return;
                }
                var isInitialized = Passport.IsInitialized();
                if (!isInitialized)
                {
                    isInitialized = Passport.StartEngine();
                    _ = SignalRClient.SendMessageAsync(
                        "SendPassportInfo",
                        obj?.ToString() ?? "",
                        "Open Result: " + (isInitialized ? "Open" : "Close")
                    );
                    Passport.StopEngine();
                }
            }
            catch (Exception e)
            {
                App.AppLogger.Error(e, e.Message);
                _ = SignalRClient.SendMessageAsync(
                    "SendPassportInfo",
                    obj?.ToString() ?? "",
                    "Open Result: " + e.Message
                );
            }
        }

        private static void OnFetchDepositInfo(object obj)
        {
            try
            {
                if (!Global.UseHardware)
                {
                    _ = SignalRClient.SendMessageAsync(
                        "SendDepositInfo",
                        obj?.ToString() ?? "",
                        "Open Result: " + (true ? "Open" : "Close")
                    );
                    return;
                }

                Instance.Dispatcher.Invoke(() =>
                {
                    var open = CashAcceptor.open_acceptor();
                    CashAcceptor.close_acceptor();
                    _ = SignalRClient.SendMessageAsync(
                        "SendDepositInfo",
                        obj?.ToString() ?? "",
                        "Open Result: " + open
                    );
                });
            }
            catch (Exception e)
            {
                App.AppLogger.Error(e, e.Message);
                _ = SignalRClient.SendMessageAsync(
                    "SendDepositInfo",
                    obj?.ToString() ?? "",
                    "Open Result: " + e.Message
                );
            }
        }

        private static void OnFetchPrinterInfo(object obj)
        {
            try
            {
                if (!Global.UseHardware)
                {
                    _ = SignalRClient.SendMessageAsync(
                        "SendPrinterInfo",
                        obj?.ToString() ?? "",
                        "Open Result: " + (true ? "Open" : "Close")
                    );
                    return;
                }

                Instance.Dispatcher.Invoke(() =>
                {
                    var open = ReceiptPrinter.IsValid();
                    _ = SignalRClient.SendMessageAsync(
                        "SendPrinterInfo",
                        obj?.ToString() ?? "",
                        "Open Result: " + open
                    );
                });
            }
            catch (Exception e)
            {
                App.AppLogger.Error(e, e.Message);
                _ = SignalRClient.SendMessageAsync(
                    "SendPrinterInfo",
                    obj?.ToString() ?? "",
                    "Open Result: " + e.Message
                );
            }
        }

        private static void OnFetchCameraInfo(object? obj)
        {
            try
            {
                if (!Global.UseHardware)
                {
                    _ = SignalRClient.SendMessageAsync(
                        "SendCameraInfo",
                        obj?.ToString() ?? "",
                        "Open Result: " + (true ? "Open" : "Close")
                    );
                    return;
                }

                Instance.Dispatcher.Invoke(() =>
                {
                    using var capture = new VideoCapture(
                        Global.CameraIndex,
                        VideoCapture.API.DShow
                    );
                    var result = capture.IsOpened;
                    _ = SignalRClient.SendMessageAsync(
                        "SendCameraInfo",
                        obj?.ToString() ?? "",
                        $"Camera Index : {Global.CameraIndex} is " + (result ? "Open" : "Close")
                    );
                });
            }
            catch (Exception e)
            {
                App.AppLogger.Error(e, e.Message);
                _ = SignalRClient.SendMessageAsync(
                    "SendCameraInfo",
                    obj?.ToString() ?? "",
                    $"Camera Index : {Global.CameraIndex} is " + e.Message
                );
            }
        }

        private static void OnFetchWithdrawInfo(object obj)
        {
            try
            {
                if (!Global.UseHardware)
                {
                    _ = SignalRClient.SendMessageAsync(
                        "SendWithdrawInfo",
                        obj?.ToString() ?? "",
                        new List<CassetteDTO>()
                        {
                            new CassetteDTO()
                            {
                                Id = "1",
                                No = 0,
                                Status = "Low Level",
                            },
                        }
                    );
                    return;
                }

                Instance.Dispatcher.Invoke(() =>
                {
                    _ = SignalRClient.SendMessageAsync(
                        "SendWithdrawInfo",
                        obj?.ToString() ?? "",
                        Dispenser.ReadCassetteStatus()
                    );
                });
            }
            catch (Exception e)
            {
                App.AppLogger.Error(e, e.Message);
                _ = SignalRClient.SendMessageAsync(
                    "SendWithdrawInfo",
                    obj?.ToString() ?? "",
                    e.Message
                );
            }
        }

        public static void InitCashDevice()
        {
            if (Global.UseHardware)
            {
                var cashDevice = CashDevice;
                cashDevice.Open("COM1");
                cashDevice.KeyExchangelimit32bit();
                cashDevice.GetUnitInfo();
                cashDevice.Connect();
            }
        }

        public static async Task DeIntSignalR()
        {
            await SignalRClient.StopConnectionAsync();
        }
    }
}
