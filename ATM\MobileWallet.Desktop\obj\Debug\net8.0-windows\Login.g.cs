﻿#pragma checksum "..\..\..\Login.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "ADE381EE26691069A0542FC99B269D26C08DF4A3"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MobileWallet.Desktop;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MobileWallet.Desktop {
    
    
    /// <summary>
    /// Login
    /// </summary>
    public partial class Login : MobileWallet.Desktop.CashDeviceWindow, System.Windows.Markup.IComponentConnector {
        
        
        #line 57 "..\..\..\Login.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox TxtUserName;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\Login.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox TxtPin;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\Login.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnLogin;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\Login.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnPrintDeposit;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\Login.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnPrintWithdraw;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MobileWallet.Desktop;component/login.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\Login.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtUserName = ((System.Windows.Controls.PasswordBox)(target));
            
            #line 55 "..\..\..\Login.xaml"
            this.TxtUserName.GotFocus += new System.Windows.RoutedEventHandler(this.GotFocus);
            
            #line default
            #line hidden
            
            #line 56 "..\..\..\Login.xaml"
            this.TxtUserName.LostFocus += new System.Windows.RoutedEventHandler(this.OnLostFocus);
            
            #line default
            #line hidden
            return;
            case 2:
            this.TxtPin = ((System.Windows.Controls.PasswordBox)(target));
            
            #line 62 "..\..\..\Login.xaml"
            this.TxtPin.LostFocus += new System.Windows.RoutedEventHandler(this.OnLostFocus);
            
            #line default
            #line hidden
            
            #line 63 "..\..\..\Login.xaml"
            this.TxtPin.GotFocus += new System.Windows.RoutedEventHandler(this.GotFocus);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BtnLogin = ((System.Windows.Controls.Button)(target));
            
            #line 78 "..\..\..\Login.xaml"
            this.BtnLogin.Click += new System.Windows.RoutedEventHandler(this.BtnLogin_OnClick);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BtnPrintDeposit = ((System.Windows.Controls.Button)(target));
            
            #line 87 "..\..\..\Login.xaml"
            this.BtnPrintDeposit.Click += new System.Windows.RoutedEventHandler(this.BtnPrintDeposit_OnClick);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BtnPrintWithdraw = ((System.Windows.Controls.Button)(target));
            
            #line 96 "..\..\..\Login.xaml"
            this.BtnPrintWithdraw.Click += new System.Windows.RoutedEventHandler(this.BtnPrintWithdraw_OnClick);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

