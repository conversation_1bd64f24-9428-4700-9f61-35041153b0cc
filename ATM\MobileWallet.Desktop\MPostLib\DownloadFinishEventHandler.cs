﻿// Decompiled with JetBrains decompiler
// Type: MPOST.DownloadFinishEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("6C3528AD-1A28-45aa-8207-CF9ED4AB848D")]
  [ComVisible(true)]
  public delegate void DownloadFinishEventHandler(object sender, AcceptorDownloadFinishEventArgs e);
}
