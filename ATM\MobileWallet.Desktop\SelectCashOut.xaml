﻿<Window x:Class="MobileWallet.Desktop.SelectCashOut"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        WindowStyle="None"
        Closing="SelectCashOut_OnClosing"
        Loaded="SelectCashOut_OnLoaded"
        Title="SelectCashOut" Height="1600" Width="900" WindowState="Maximized">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="150"/>
            <RowDefinition Height="150"/>
            <RowDefinition Name="DynamicHeight" Height="500"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <Image Grid.Column="0" Grid.Row="0" Grid.RowSpan="4" Stretch="Fill" Source="./images/back-drop.png "/>
        <Image Grid.Row="0" Width="250" Source="./images/logo.png" />

        <TextBlock Name="SelectCashInOut" HorizontalAlignment="Center" Grid.Row="1" Margin="0 20 0 0" FontWeight="Bold" Foreground="#ffffff" FontSize="50" TextWrapping="Wrap" Text="Select Cash In / Out" />

        <StackPanel Grid.Row="2" HorizontalAlignment="Right" VerticalAlignment="Center">
            <Border Margin="0 0 20 0"
                    Name="BtnDepositBorder"
                    HorizontalAlignment="Right" Grid.Row="0" Grid.RowSpan="3" BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
                <Button Margin="0 0 0 0" Name="Deposit" BorderThickness="0" Background="Transparent" Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                        Click="Button_Click_Deposit"  >Deposit</Button>
            </Border>
            <Border 
                Name="BtnWithdrawBorder"
                Margin="0 50 20 0" HorizontalAlignment="Right"  Grid.Row="0" Grid.RowSpan="3" BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
                <Button Margin="0 0 0 0" Name="Withdraw" BorderThickness="0" Background="Transparent"  Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                        Click="Button_Click_Withdrawl"  >Withdraw</Button>
            </Border>
        </StackPanel>
        <StackPanel VerticalAlignment="Center" HorizontalAlignment="Left" Grid.Row="2">
            <Border Margin="20 00 0 0" HorizontalAlignment="Left" Grid.Row="0" Grid.RowSpan="3" BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
                <Button Margin="0 0 0 0" Name="Back" BorderThickness="0" Background="Transparent" Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                        Click="Button_Click_Back"  >Back</Button>
            </Border>
            <Border Margin="20 50 0 0" HorizontalAlignment="Left"  Grid.Row="0" Grid.RowSpan="3" BorderBrush="#FC5353" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
                <Button Margin="0 0 0 0" Name="Cancel" BorderThickness="0" Background="Transparent" FontWeight="DemiBold" Foreground="#FC5353" FontSize="35"
                        Click="Button_Click_Cancel"  >Cancel</Button>
            </Border>    
        </StackPanel>
        


    </Grid>
</Window>
