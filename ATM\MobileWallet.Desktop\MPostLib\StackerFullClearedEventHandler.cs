﻿// Decompiled with JetBrains decompiler
// Type: MPOST.StackerFullClearedEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("314F018F-4682-4d97-BD06-0A47693B6CC0")]
  [ComVisible(true)]
  public delegate void Stacker<PERSON>ullClearedEventHandler(object sender, EventArgs e);
}
