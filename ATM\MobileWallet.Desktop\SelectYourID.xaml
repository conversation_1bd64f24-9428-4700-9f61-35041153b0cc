﻿<Window x:Class="MobileWallet.Desktop.SelectYourID"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Loaded="SelectYourID_OnLoaded"
        Closing="SelectYourID_OnClosing"
        WindowStyle="None"
        Title="SelectYourID" Width="900" Height="1600" WindowState="Maximized">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="150"/>
            <RowDefinition Height="150"/>
            <RowDefinition Name="DynamicRow" Height="500"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <Image Grid.Column="0" Grid.Row="0" Grid.RowSpan="4" Stretch="Fill" Source="./images/back-drop.png "/>
        <Image Grid.Row="0" Width="250" Source="./images/logo.png" />

        <TextBlock Name="SelectIDScanner" HorizontalAlignment="Center"
                   TextAlignment="Center"
                   Grid.Row="1" Margin="0 0 0 0" FontWeight="Bold" Foreground="#ffffff" FontSize="50" TextWrapping="Wrap" Text="Select Your Identification Method" />

        <StackPanel Grid.Row="2" HorizontalAlignment="Right" VerticalAlignment="Center">
            <Border Margin="0 0 20 0" HorizontalAlignment="Right" Grid.Row="0" Grid.RowSpan="3" BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
                <Button Margin="0 0 0 0" Name="IDCard" BorderThickness="0" Background="Transparent" Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                        Click="Button_Click_1">ID Card</Button>
            </Border>
            <Border Margin="0 50 20 0" HorizontalAlignment="Right"  Grid.Row="0" Grid.RowSpan="3" BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
                <Button Margin="0 0 0 0" Name="Passport" BorderThickness="0" Background="Transparent"  Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                        Click="Button_Click_Passport">Passport</Button>
            </Border>    
        </StackPanel>
        <!-- <Border Margin="0 700 20 0" HorizontalAlignment="Right"  Grid.Row="0" Grid.RowSpan="3" BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" > -->
        <!--     <Button Margin="0 0 0 0" Name="DrivingLicence" BorderThickness="0" Background="Transparent"  Foreground="#5387FC" FontWeight="DemiBold" FontSize="35" -->
        <!--             Click="Button_Click_Driving_Licence" >Driving Licence</Button> -->
        <!-- </Border> -->
        <StackPanel Grid.Row="2" HorizontalAlignment="Left" VerticalAlignment="Center">
            <Border Margin="20 0 0 0" HorizontalAlignment="Left" Grid.Row="0" Grid.RowSpan="3" BorderBrush="#5387FC" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
                <Button Margin="0 0 0 0" Name="Back" BorderThickness="0" Background="Transparent" Foreground="#5387FC" FontWeight="DemiBold" FontSize="35"
                        Click="Button_Click_Back">Back</Button>
            </Border>
            <Border Margin="20 50 0 0" HorizontalAlignment="Left"  Grid.Row="0" Grid.RowSpan="3" BorderBrush="#FC5353" BorderThickness="4" Width="280" Background="#ffffff" Height="80" CornerRadius="10" >
                <Button Name="Cancel" BorderThickness="0" Background="Transparent" FontWeight="DemiBold" Foreground="#FC5353" FontSize="35"
                        Click="Button_Click_2" Height="72" VerticalAlignment="Top"  >Cancel</Button>
            </Border>
        </StackPanel>
    </Grid>
</Window>
