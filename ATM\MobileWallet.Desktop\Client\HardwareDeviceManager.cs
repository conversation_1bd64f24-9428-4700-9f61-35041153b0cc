using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Threading;
using NLog;

namespace MobileWallet.Desktop.Client
{
    /// <summary>
    /// Manages hardware device connections and prevents timeout-related crashes during idle periods
    /// </summary>
    public static class HardwareDeviceManager
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();
        private static DispatcherTimer? _deviceKeepAliveTimer;
        private static readonly TimeSpan DeviceKeepAliveInterval = TimeSpan.FromMinutes(3); // Keep devices alive every 3 minutes
        private static bool _isInitialized = false;
        private static readonly object _lockObject = new object();

        /// <summary>
        /// Initialize hardware device management with keep-alive functionality
        /// </summary>
        public static void Initialize()
        {
            lock (_lockObject)
            {
                if (_isInitialized)
                    return;

                try
                {
                    // Initialize device keep-alive timer
                    _deviceKeepAliveTimer = new DispatcherTimer();
                    _deviceKeepAliveTimer.Interval = DeviceKeepAliveInterval;
                    _deviceKeepAliveTimer.Tick += DeviceKeepAliveTimer_Tick;
                    _deviceKeepAliveTimer.Start();

                    _isInitialized = true;
                    Logger.Info("Hardware device manager initialized with keep-alive functionality");
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, "Failed to initialize hardware device manager");
                }
            }
        }

        /// <summary>
        /// Shutdown hardware device management
        /// </summary>
        public static void Shutdown()
        {
            lock (_lockObject)
            {
                try
                {
                    _deviceKeepAliveTimer?.Stop();
                    _deviceKeepAliveTimer = null;
                    _isInitialized = false;
                    Logger.Info("Hardware device manager shutdown completed");
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, "Error during hardware device manager shutdown");
                }
            }
        }

        /// <summary>
        /// Keep-alive timer to maintain hardware device connections
        /// </summary>
        private static async void DeviceKeepAliveTimer_Tick(object? sender, EventArgs e)
        {
            try
            {
                await PerformDeviceKeepAlive();
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error in device keep-alive timer");
            }
        }

        /// <summary>
        /// Perform keep-alive operations on all hardware devices
        /// </summary>
        private static async Task PerformDeviceKeepAlive()
        {
            try
            {
                Logger.Debug("Performing device keep-alive operations");

                // Keep cash device alive
                await KeepCashDeviceAlive();

                // Keep SignalR connection alive
                await KeepSignalRAlive();

                Logger.Debug("Device keep-alive operations completed");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error during device keep-alive operations");
            }
        }

        /// <summary>
        /// Keep cash device connection alive
        /// </summary>
        private static async Task KeepCashDeviceAlive()
        {
            try
            {
                if (!Global.UseHardware || !Global.UseV2)
                    return;

                var cashDevice = App.CashDevice;
                if (cashDevice != null)
                {
                    // Send a simple status query to keep connection alive
                    try
                    {
                        cashDevice.GetUnitInfo();
                        Logger.Debug("Cash device keep-alive ping successful");
                    }
                    catch (Exception ex)
                    {
                        Logger.Warn(ex, "Cash device keep-alive ping failed, attempting reconnection");
                        await ReconnectCashDevice();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error in cash device keep-alive");
            }
        }

        /// <summary>
        /// Reconnect cash device if connection is lost
        /// </summary>
        private static async Task ReconnectCashDevice()
        {
            try
            {
                Logger.Info("Attempting to reconnect cash device");

                await Task.Run(() =>
                {
                    try
                    {
                        var cashDevice = App.CashDevice;
                        
                        // Close existing connection
                        cashDevice.Close();
                        
                        // Wait a moment before reconnecting
                        Thread.Sleep(2000);
                        
                        // Reinitialize connection
                        cashDevice.Open("COM1");
                        cashDevice.KeyExchangelimit32bit();
                        cashDevice.GetUnitInfo();
                        cashDevice.Connect();
                        
                        Logger.Info("Cash device reconnection successful");
                    }
                    catch (Exception ex)
                    {
                        Logger.Error(ex, "Cash device reconnection failed");
                    }
                });
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error during cash device reconnection attempt");
            }
        }

        /// <summary>
        /// Keep SignalR connection alive
        /// </summary>
        private static async Task KeepSignalRAlive()
        {
            try
            {
                var signalRClient = App.SignalRClient;
                if (signalRClient != null)
                {
                    // Send a keep-alive ping
                    try
                    {
                        await signalRClient.SendMessageAsync("KeepAlive", "ping");
                        Logger.Debug("SignalR keep-alive ping successful");
                    }
                    catch (Exception ex)
                    {
                        Logger.Warn(ex, "SignalR keep-alive ping failed, attempting reconnection");
                        await ReconnectSignalR();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error in SignalR keep-alive");
            }
        }

        /// <summary>
        /// Reconnect SignalR if connection is lost
        /// </summary>
        private static async Task ReconnectSignalR()
        {
            try
            {
                if (string.IsNullOrEmpty(TokenManager.AccessToken))
                {
                    Logger.Warn("Cannot reconnect SignalR - no access token available");
                    return;
                }

                Logger.Info("Attempting to reconnect SignalR");

                var signalRClient = App.SignalRClient;
                await signalRClient.StopConnectionAsync();
                await signalRClient.InitializeConnectionAsync(TokenManager.AccessToken);

                Logger.Info("SignalR reconnection successful");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "SignalR reconnection failed");
            }
        }

        /// <summary>
        /// Gracefully prepare devices for idle state
        /// </summary>
        public static Task PrepareDevicesForIdle()
        {
            try
            {
                Logger.Info("Preparing devices for idle state");

                if (Global.UseHardware && Global.UseV2)
                {
                    var cashDevice = App.CashDevice;
                    if (cashDevice != null)
                    {
                        // Disable acceptor but keep connection alive
                        cashDevice.DisableAcceptor();
                        Logger.Debug("Cash device prepared for idle state");
                    }
                }

                Logger.Info("Devices prepared for idle state");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error preparing devices for idle state");
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// Reactivate devices when user returns
        /// </summary>
        public static async Task ReactivateDevicesFromIdle()
        {
            try
            {
                Logger.Info("Reactivating devices from idle state");

                if (Global.UseHardware && Global.UseV2)
                {
                    var cashDevice = App.CashDevice;
                    if (cashDevice != null)
                    {
                        // Attempt to reconnect the cash device
                        try
                        {
                            cashDevice.GetUnitInfo();
                            Logger.Debug("Cash device is responsive");
                        }
                        catch (Exception ex)
                        {
                            Logger.Warn(ex, "Cash device not responsive, attempting reconnection");
                            await ReconnectCashDevice();
                        }
                        Logger.Debug("Cash device reactivated from idle state");
                    }
                }

                Logger.Info("Devices reactivated from idle state");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error reactivating devices from idle state");
            }
        }
    }
}
