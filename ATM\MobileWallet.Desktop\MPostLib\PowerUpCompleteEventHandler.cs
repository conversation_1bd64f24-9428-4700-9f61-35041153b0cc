﻿// Decompiled with JetBrains decompiler
// Type: MPOST.PowerUpCompleteEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("DAC02AB2-E4D7-4470-B953-4824176CDCCD")]
  [ComVisible(true)]
  public delegate void PowerUpCompleteEventHandler(object sender, EventArgs e);
}
