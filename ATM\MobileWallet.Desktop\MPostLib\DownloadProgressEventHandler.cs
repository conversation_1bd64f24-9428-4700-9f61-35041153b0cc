﻿// Decompiled with JetBrains decompiler
// Type: MPOST.DownloadProgressEventHandler
// Assembly: MPOST, Version=3.0.9.1, Culture=neutral, PublicKeyToken=null
// MVID: F8C15F18-2FEE-452E-9333-842EC1A1B67F
// Assembly location: C:\Users\<USER>\Downloads\MPOST.dll

using System.Runtime.InteropServices;

namespace MobileWallet.Desktop.MPostLib
{
  [Guid("0907B833-D10B-44e1-A9FD-39D0DBAB8F8C")]
  [ComVisible(true)]
  public delegate void DownloadProgressEventHandler(object sender, AcceptorDownloadEventArgs e);
}
