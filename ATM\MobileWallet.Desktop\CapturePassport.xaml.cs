﻿using System.ComponentModel;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Documents;
using MobileWallet.Desktop.API;
using MobileWallet.Desktop.Atm;
using Newtonsoft.Json;

namespace MobileWallet.Desktop;

public partial class CapturePassport : Window
{
    private bool _isRetake = false;
    private bool _isEngineStarted = false;

    public CapturePassport()
    {
        InitializeComponent();
        Set_Language();
        SubmitBorder.Visibility = Visibility.Hidden;
    }

    private void Set_Language()
    {
        switch (Global.DefaultLanguage)
        {
            case "English":
                BtnCapture.Content = ResourceEnglish.Capture;
                BtnBack.Content = ResourceEnglish.Back;
                BtnCancel.Content = ResourceEnglish.Cancel;
                BtnSubmit.Content = ResourceEnglish.Submit;
                WindowTitle.Text = ResourceEnglish.PlacePassportInsideCamera;
                break;
            case "French":
                BtnCapture.Content = ResourceFrench.Capture;
                BtnBack.Content = ResourceFrench.Back;
                BtnCancel.Content = ResourceFrench.Cancel;
                BtnSubmit.Content = ResourceFrench.Submit;
                WindowTitle.Text = ResourceFrench.PlacePassportInsideCamera;
                break;
        }
    }

    private void Button_Click(object sender, RoutedEventArgs e)
    {
        _ = App.LogError("Back Button Pressed on Camera Page", LogType.Back);
        SelectYourID NewWindow = new SelectYourID();
        NewWindow.Show();
        this.Close();
    }

    private void Button_Click_Cancel(object sender, RoutedEventArgs e)
    {
        _ = App.LogError("Cancel Button Pressed on Camera Page", LogType.Cancel);
        WelcomeToMobileWallet NewWindow = new WelcomeToMobileWallet();
        NewWindow.Show();
        this.Close();
    }

    private void Button_Click_Capture(object sender, RoutedEventArgs e)
    {
        try
        {
            App.ShowProcessingDialog();
            //Camera Implementation
            if (Global.UseHardware)
            {
                if (!App.Passport.IsInitialized())
                {
                    if (!_isEngineStarted)
                    {
                        if (!App.Passport.StartEngine())
                        {
                            Console.WriteLine("Failed to start the reader engine.");
                            CustomMessageBox.Show(
                                "Passport/Id Card Scanner is not working at the moment."
                            );
                        }
                        _isEngineStarted = true;
                    }
                    App.Passport.SetDataConfig(
                        new PassportReaderV2.DataGroupConfig()
                        {
                            needMRZ = true,
                            needOCR = false,
                            needRFID = false,
                        }
                    );
                    if (!App.Passport.RegisterPassportCallback(PassportCallback))
                    {
                        Console.WriteLine("Failed to RegisterPassportCallback.");
                        CustomMessageBox.Show(
                            "Passport/Id Card Scanner is not working at the moment."
                        );
                    }

                    if (!App.Passport.RegisterNotifyCallback(NotificationCallback))
                    {
                        Console.WriteLine("Failed to RegisterPassportCallback.");
                        CustomMessageBox.Show(
                            "Passport/Id Card Scanner is not working at the moment."
                        );
                    }
                    if (!App.Passport.Initialize())
                    {
                        Console.WriteLine("Failed to start the reader engine.");
                        CustomMessageBox.Show(
                            "Passport/Id Card Scanner is not working at the moment."
                        );
                    }

                    // var passportDelegate = new PassportReaderV2.PassportCallbackResultData(
                    //     PassportCallback
                    // );
                    // var notificationDelegate = new PassportReaderV2.NotifyCallbackResultData(
                    //     NotificationCallback
                    // );
                    // App.Passport.PassportDelegateHandle = GCHandle.Alloc(passportDelegate);
                    // App.Passport.NotificationDelegateHandle = GCHandle.Alloc(notificationDelegate);
                }
                var r = App.Passport.ManualRead();
                if (r)
                {
                    App.ShowProcessingDialogWithMessage(
                        ResourceEnglish.ProcessingRequest,
                        ResourceFrench.ProcessingRequest
                    );
                    return;
                }
                else
                {
                    App.HideProcessingDialog();
                    CustomMessageBox.Show("Unable to Read Passport/ID Card");
                }
                return;
            }
            else
            {
                SubmitBorder.Visibility = Visibility.Visible;
                CaptureBorder.Visibility = Visibility.Hidden;
            }
            App.HideProcessingDialog();
        }
        catch (Exception ex)
        {
                App.AppLogger.Error(ex,ex.Message);
            App.HideProcessingDialog();
            CustomMessageBox.Show("An error occurred: " + ex.Message, "Error");
        }
    }

    private void NotificationCallback(IntPtr context, int notifyid, int errcode, IntPtr data)
    {
        if (errcode == 8)
        {
            App.Instance.Dispatcher.Invoke(() =>
            {
                App.HideProcessingDialog();
            });
        }
        var dataString = Marshal.PtrToStringAnsi(data);
    }

    private void PassportCallback(IntPtr context, ref PassportReaderV2.PassportInfo passportinfo)
    {
        Global.SelectedPassPort = JsonConvert.DeserializeObject<PassportReaderV2.PassportInfo>(
            JsonConvert.SerializeObject(passportinfo)
        );
        _ = Task.Run(() =>
        {
            try
            {
                if (
                    string.IsNullOrWhiteSpace(Global.SelectedPassPort.Value.MRZInfo.GivenName)
                    && string.IsNullOrWhiteSpace(Global.SelectedPassPort.Value.MRZInfo.Surname)
                )
                {
                    Dispatcher.Invoke(() =>
                    {
                        App.HideProcessingDialog();
                        Global.SelectedPassPort = null;
                    });
                    return;
                }

                Dispatcher.Invoke(() =>
                {
                    var passport = Global.SelectedPassPort;
                    App.HideProcessingDialog();
                    TxtPassportDetails.Inlines.Clear();
                    var name =
                        passport?.MRZInfo.GivenName != ""
                            ? passport?.MRZInfo.GivenName
                            : passport?.MRZInfo.Surname;
                    TxtPassportDetails.Inlines.Add("Name: " + name);
                    TxtPassportDetails.Inlines.Add(new LineBreak());
                    TxtPassportDetails.Inlines.Add("Gender: " + passport?.MRZInfo.Sex);
                    TxtPassportDetails.Inlines.Add(new LineBreak());
                    TxtPassportDetails.Inlines.Add("Doc No: " + passport?.MRZInfo.DocumentNo);
                    TxtPassportDetails.Inlines.Add(new LineBreak());
                    this.SubmitBorder.Visibility = Visibility.Visible;
                    this.CaptureBorder.Visibility = Visibility.Hidden;
                });
            }
            catch (Exception e)
            {
                App.AppLogger.Error(e,e.Message);
                Console.WriteLine(e);
            }
        });
    }

    private void Button_Click_Submit(object sender, RoutedEventArgs e)
    {
        if (Global.UseHardware)
        {
            _ = App.UploadPassportInfoIfExist();
        }
        if (Global.IsCrypto)
        {
            SelectCryptoNetwork window = new SelectCryptoNetwork();
            window.Show();
        }
        else
        {
            EnterAccountNumber window = new EnterAccountNumber();
            window.Show();
        }
        Close();
    }

    private void CapturePassport_OnClosing(object? sender, CancelEventArgs e)
    {
        App.StopTimer();
        if (App.Passport.IsInitialized())
        {
            App.Passport.DeInitialize();
            App.Passport.StopEngine();
        }
    }

    private void CapturePassport_OnLoaded(object sender, RoutedEventArgs e)
    {
        Helper.AdjustRowHeight(this, DynamicRow);
        App.StartTimer(this);
        _ = App.TrackAtmRealTime(
            new UpdateAtmRealTimeRequestModel() { CurrentScreen = nameof(CapturePassport) }
        );
    }
}
